# 最小化 OpenTelemetry Java Agent 需求文档

## 1. 项目概述

### 1.1 项目背景
实现一个最小化的 OpenTelemetry Java Agent，专注核心追踪功能。参考 [OpenTelemetry Java Instrumentation](https://github.com/open-telemetry/opentelemetry-java-instrumentation) 的架构模式，但去除复杂功能，仅保留链路追踪。

### 1.2 目标
- **最小化实现**: 仅保留 Tracer + Span 核心概念
- **零侵入追踪**: HTTP 请求 → 数据库操作 的完整链路
- **OpenTelemetry 兼容**: 使用标准 OpenTelemetry API 结构
- **轻量化部署**: 单 JAR 文件 + JVM 参数启动

### 1.3 范围
- **目标应用**: 当前 sample 模块的 Spring Boot 应用
- **追踪层级**: HTTP Controller + JDBC PreparedStatement
- **输出方式**: 控制台 JSON 格式日志 (模仿 OTLP 格式)
- **部署方式**: `-javaagent:agent.jar` 参数

## 2. OpenTelemetry 标准实现

### 2.1 核心概念映射

基于 OpenTelemetry 标准，实现以下核心组件：

| OpenTelemetry 概念 | 最小化实现 |
|---|---|
| `TracerProvider` | 全局单例，管理 Tracer 实例 |
| `Tracer` | 创建和管理 Span 的入口 |
| `Span` | 代表一个操作的执行单元 |
| `SpanContext` | 包含 traceId, spanId, traceFlags |
| `Context` | ThreadLocal 存储，跨方法传递 |

### 2.2 HTTP 请求追踪 (根 Span)

**实现**: 拦截 Spring Controller 方法

**Span 属性** (参考 OpenTelemetry HTTP 语义约定):
```json
{
  "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
  "spanId": "00f067aa0ba902b7", 
  "parentSpanId": null,
  "operationName": "GET /api/users",
  "startTime": 1692614400000,
  "endTime": 1692614400156,
  "duration": 156,
  "attributes": {
    "http.method": "GET",
    "http.url": "/api/users",
    "http.status_code": 200,
    "component": "spring-web"
  }
}
```

### 2.3 数据库追踪 (子 Span)

**实现**: 拦截 JDBC PreparedStatement

**Span 属性** (参考 OpenTelemetry DB 语义约定):
```json
{
  "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
  "spanId": "b9c7c989f97918e1",
  "parentSpanId": "00f067aa0ba902b7",
  "operationName": "SELECT users",
  "startTime": 1692614400020,
  "endTime": 1692614400032,
  "duration": 12,
  "attributes": {
    "db.system": "postgresql",
    "db.statement": "SELECT * FROM users WHERE id = ?",
    "db.operation": "SELECT",
    "component": "jdbc"
  }
}
```

### 2.4 Context 传递机制

**实现**: 参考 OpenTelemetry Context API

```java
public class TraceContext {
    private final String traceId;
    private final String spanId;
    private final String parentSpanId;
    private final Map<String, Object> baggage;
    
    // 静态 ThreadLocal 存储
    private static final ThreadLocal<TraceContext> CURRENT = new ThreadLocal<>();
    
    public static TraceContext current() {
        return CURRENT.get();
    }
    
    public static Scope makeCurrent(TraceContext context) {
        return new Scope(() -> CURRENT.set(context));
    }
}
```

## 3. 技术实现架构

### 3.1 依赖和技术栈

```gradle
dependencies {
    implementation 'net.bytebuddy:byte-buddy:1.12.+' // 字节码插桩
    implementation 'io.opentelemetry:opentelemetry-api:1.52.0' // OpenTelemetry API (仅使用接口定义)
    compileOnly 'org.springframework:spring-web:5.+' // Spring 注解支持
    compileOnly 'java.sql:java.sql' // JDBC API
}
```

### 3.2 Agent 架构设计

参考 OpenTelemetry Java Instrumentation 架构：

```
agent.jar
├── Agent.java (premain 入口)
├── instrumentation/
│   ├── SpringControllerInstrumentation.java
│   └── JdbcInstrumentation.java  
├── context/
│   ├── TraceContext.java
│   └── ContextManager.java
├── span/
│   ├── SimpleSpan.java
│   └── SpanBuilder.java
└── exporter/
    └── ConsoleSpanExporter.java
```

### 3.3 核心实现类

#### 3.3.1 Agent 入口 (参考 OpenTelemetry Agent)
```java
public class Agent {
    public static void premain(String agentArgs, Instrumentation inst) {
        System.out.println("[Agent] OpenTelemetry Minimal Agent starting...");
        
        // 初始化全局 TracerProvider
        GlobalTracer.initialize();
        
        // 注册插桩
        new AgentBuilder.Default()
            .type(ElementMatchers.isAnnotatedWith(
                named("org.springframework.web.bind.annotation.RestController")
                .or(named("org.springframework.stereotype.Controller"))))
            .transform(new SpringControllerTransformer())
            .type(named("java.sql.PreparedStatement"))
            .transform(new JdbcTransformer())
            .installOn(inst);
            
        System.out.println("[Agent] OpenTelemetry Minimal Agent started");
    }
}
```

#### 3.3.2 Span 实现 (简化版 OpenTelemetry Span)
```java
public class SimpleSpan {
    private final String traceId;
    private final String spanId;
    private final String parentSpanId;
    private final String operationName;
    private final long startTimeNanos;
    private volatile long endTimeNanos;
    private final Map<String, Object> attributes = new HashMap<>();
    private volatile SpanStatus status = SpanStatus.UNSET;
    
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    public void setStatus(SpanStatus status) {
        this.status = status;
    }
    
    public void end() {
        this.endTimeNanos = System.nanoTime();
        // 导出到 Console
        ConsoleSpanExporter.export(this);
    }
}
```

#### 3.3.3 拦截器实现
```java
// HTTP 拦截器
@RuntimeType
public static Object interceptController(
    @Origin Method method,
    @AllArguments Object[] args,
    @SuperCall Callable<?> callable) throws Exception {
    
    Span span = GlobalTracer.get()
        .spanBuilder(method.getDeclaringClass().getSimpleName() + "." + method.getName())
        .startSpan();
        
    try (Scope scope = span.makeCurrent()) {
        // 设置 HTTP 属性
        span.setAttribute("http.method", extractHttpMethod(method));
        span.setAttribute("component", "spring-web");
        
        Object result = callable.call();
        span.setAttribute("http.status_code", 200);
        return result;
    } catch (Exception e) {
        span.setStatus(SpanStatus.ERROR);
        span.setAttribute("error.message", e.getMessage());
        throw e;
    } finally {
        span.end();
    }
}
```

## 4. 验证方案

### 4.1 构建配置

#### build.gradle.kts (agent 模块)
```kotlin
plugins {
    id("com.github.johnrengelman.shadow") version "7.1.2"
}

dependencies {
    implementation("net.bytebuddy:byte-buddy:1.12.20")
    compileOnly("org.springframework:spring-web:5.3.21")
}

tasks.jar {
    manifest {
        attributes(
            "Premain-Class" to "ai.servicewall.agent.Agent",
            "Can-Redefine-Classes" to "true",
            "Can-Retransform-Classes" to "true"
        )
    }
}

shadowJar {
    // 避免依赖冲突
    relocate("net.bytebuddy", "ai.servicewall.shaded.bytebuddy")
}
```

### 4.2 验证步骤

```bash
# 1. 构建 Agent
./gradlew :agent:shadowJar

# 2. 启动应用 (带 Agent)
java -javaagent:agent/build/libs/agent-all.jar \
     -jar sample/build/libs/sample.jar

# 3. 发送测试请求
curl http://localhost:8080/api/users/123
```

### 4.3 预期输出 (OpenTelemetry JSON 格式)

**完整追踪链路**:
```json
{
  "resourceSpans": [{
    "resource": {
      "attributes": [
        {"key": "service.name", "value": "sample-app"}
      ]
    },
    "scopeSpans": [{
      "scope": {
        "name": "minimal-agent", 
        "version": "1.0.0"
      },
      "spans": [
        {
          "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
          "spanId": "00f067aa0ba902b7",
          "parentSpanId": "",
          "name": "GET /api/users/{id}",
          "kind": "SERVER", 
          "startTimeUnixNano": 1692614400000000000,
          "endTimeUnixNano": 1692614400156000000,
          "attributes": [
            {"key": "http.method", "value": "GET"},
            {"key": "http.url", "value": "/api/users/123"},
            {"key": "http.status_code", "value": 200},
            {"key": "component", "value": "spring-web"}
          ],
          "status": {"code": "STATUS_CODE_OK"}
        },
        {
          "traceId": "4bf92f3577b34da6a3ce929d0e0e4736", 
          "spanId": "b9c7c989f97918e1",
          "parentSpanId": "00f067aa0ba902b7",
          "name": "SELECT users",
          "kind": "CLIENT",
          "startTimeUnixNano": 1692614400020000000,
          "endTimeUnixNano": 1692614400032000000,
          "attributes": [
            {"key": "db.system", "value": "postgresql"},
            {"key": "db.statement", "value": "SELECT * FROM users WHERE id = ?"},
            {"key": "db.operation", "value": "SELECT"},
            {"key": "component", "value": "jdbc"}
          ],
          "status": {"code": "STATUS_CODE_OK"}
        }
      ]
    }]
  }]
}
```

### 4.4 最小交付物

- **agent/build/libs/agent-all.jar** (Shadow JAR, < 2MB)
- **run-with-agent.sh** 启动脚本
- 核心类 (总计 < 10 个文件):
  - `Agent.java` - 主入口
  - `SimpleSpan.java` - Span 实现  
  - `TraceContext.java` - 上下文管理
  - `SpringControllerInstrumentation.java` - HTTP 拦截
  - `JdbcInstrumentation.java` - 数据库拦截
  - `ConsoleSpanExporter.java` - JSON 输出

### 4.5 成功标准

✅ **功能验证**:
- Agent 成功加载，不影响应用启动
- HTTP 请求生成根 span，包含正确的 traceId
- 数据库操作生成子 span，正确关联到父 span
- JSON 输出符合 OpenTelemetry 格式规范

✅ **性能验证**:  
- 应用启动时间增加 < 2 秒
- HTTP 请求响应时间增加 < 50ms
- Agent JAR 大小 < 5MB

---

**参考文档**: [OpenTelemetry Java Instrumentation](https://github.com/open-telemetry/opentelemetry-java-instrumentation)  
**最后更新**: 2025-08-21  
**状态**: ✅ 实现完成 - OpenTelemetry 标准兼容的最小化 Java Agent
