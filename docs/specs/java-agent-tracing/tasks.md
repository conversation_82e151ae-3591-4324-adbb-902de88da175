# ServiceWall Java Agent 实现任务清单

## 项目概述
基于 agent 模块完成对 sample 应用的完整调用链路追踪，实现一个简化版的 OpenTelemetry Java Agent。

## 核心目标
- 实现零侵入式的应用性能监控和链路追踪
- 支持 HTTP 请求和数据库操作的完整追踪
- 提供格式化的控制台日志输出
- 简化部署和使用流程

## 任务分解

### 1. 项目分析和准备
**状态**: 🔄 进行中
**说明**: 深入分析 OpenTelemetry Java Agent 源码，理解字节码插桩原理
**优先级**: 高
**预计工时**: 2-3 天

**具体任务**:
1. **源码分析 (1天)**
   - 访问 https://github.com/open-telemetry/opentelemetry-java-instrumentation
   - 分析 instrumentation 目录结构
   - 理解 Agent 加载和初始化流程
   - 研究拦截器实现模式

2. **技术选型 (0.5天)**
   - 对比 ASM vs ByteBuddy 字节码操作库
   - 评估依赖冲突风险 (Slf4j, Guava 等)
   - 确定 JDK 8 兼容性要求
   - 选择轻量化实现方案

3. **Spring Boot 架构分析 (1天)**
   - 分析 sample 应用 Controller 层结构
   - 理解 JPA Repository 调用流程
   - 确定拦截点和数据收集点
   - 设计不影响应用功能的插桩方案

4. **设计文档编写 (0.5天)**
   - 编写技术设计文档
   - 绘制架构图
   - 定义数据结构和接口
   - 制定开发计划

**交付物**:
- 技术分析报告
- 架构设计文档
- 开发任务清单

### 2. Agent 核心架构设计
**状态**: 待开始
**说明**: 设计轻量级 Agent 架构，避免依赖冲突
**优先级**: 高
**预计工时**: 1-2 天

**具体任务**:
1. **核心类设计 (0.5天)**
   - 设计 Agent 主入口类
   - 定义 ClassFileTransformer 接口
   - 设计拦截器抽象基类
   - 规划配置管理机制

2. **数据结构定义 (0.5天)**
   - 设计 TraceID 生成算法
   - 定义 Span 数据结构
   - 设计 Context 传递机制
   - 规划线程安全方案

3. **错误处理策略 (0.5天)**
   - 设计异常捕获和处理流程
   - 实现降级策略
   - 规划日志记录机制
   - 定义错误码和错误信息

4. **模块化架构设计 (0.5天)**
   - 划分功能模块边界
   - 设计模块间通信接口
   - 规划扩展机制
   - 设计插件化架构

**交付物**:
- 详细设计文档
- 类图和接口定义
- 架构图
- 数据库设计 (如需要)

### 3. 字节码插桩功能实现
**状态**: 待开始
**说明**: 实现运行时字节码修改和方法拦截
**具体任务**:
- 实现 ClassFileTransformer
- 开发 Controller 类拦截器
- 开发 Repository 类拦截器
- 实现方法执行时间统计

### 4. HTTP 请求追踪功能
**状态**: 待开始
**说明**: 拦截并追踪 HTTP 请求的完整生命周期
**具体任务**:
- 自动生成唯一 traceID
- 记录请求开始/结束时间
- 捕获请求参数和响应状态
- 维护父子 Span 关系
- 实现线程安全上下文传递

### 5. 数据库操作追踪功能
**状态**: 待开始
**说明**: 追踪 JPA Repository 的数据库操作
**具体任务**:
- 拦截 Repository 方法调用
- 记录方法执行时间和参数
- 关联到 HTTP 请求上下文
- 实现 SQL 语句捕获

### 6. 追踪数据输出和格式化
**状态**: 待开始
**说明**: 实现格式化的控制台日志输出
**具体任务**:
- 设计日志格式和样式
- 实现分层日志输出
- 添加时间戳和 traceID
- 支持不同日志级别

### 7. Agent 打包和部署
**状态**: 待开始
**说明**: 创建可部署的 Agent JAR 包
**具体任务**:
- 配置 Gradle 构建脚本
- 创建 Fat JAR 打包配置
- 编写启动脚本
- 实现一键部署流程

### 8. Makefile 命令系统
**状态**: 待开始
**说明**: 创建完整的项目管理命令集
**具体任务**:
- 实现核心命令：build、run、demo
- 添加辅助命令：clean、test、help
- 集成数据库服务管理
- 提供测试验证命令

### 9. 功能测试和验证
**状态**: 待开始
**说明**: 端到端测试完整追踪功能
**具体任务**:
- Agent 加载测试
- HTTP 请求追踪测试
- 数据库操作追踪测试
- 性能影响测试
- 错误场景测试

### 10. 文档完善和优化
**状态**: 待开始
**说明**: 完善项目文档和使用指南
**具体任务**:
- 优化需求文档结构
- 更新任务清单
- 编写详细使用指南
- 创建 API 文档

## 🎯 技术要求

### 开发环境要求
- **Java**: JDK 1.8+
- **构建工具**: Gradle
- **容器化**: Docker
- **数据库**: PostgreSQL

### 核心技术点
- **ByteBuddy**: 字节码插桩和修改
- **JVM TI**: Agent 加载和类转换
- **ThreadLocal**: 线程上下文传递
- **Spring Boot 2.x**: 目标应用框架

## 📊 验收标准

### 功能验收
- [ ] Agent 能成功加载到 JVM
- [ ] HTTP 请求能被正确追踪
- [ ] 数据库操作能被正确追踪
- [ ] 追踪信息格式化输出到控制台
- [ ] 所有 Makefile 命令正常工作

### 性能验收
- [ ] Agent 性能开销 < 10%
- [ ] 不影响应用正常功能
- [ ] 错误处理机制完善

### 文档验收
- [ ] 需求文档完整清晰
- [ ] 使用指南详细易懂
- [ ] Makefile 命令完整可用

## 🚀 实施建议

### 开发顺序建议
1. 先完成项目分析和架构设计
2. 实现 Agent 核心框架
3. 开发字节码插桩功能
4. 实现 HTTP 和数据库追踪
5. 添加日志输出和格式化
6. 创建打包和部署配置
7. 完善测试和文档

### 风险控制
- **依赖冲突**: 最小化外部依赖，使用核心技术栈
- **性能影响**: 实现轻量化插桩，避免过度拦截
- **兼容性**: 确保 JDK 8 兼容性，避免使用新版本特性
- **错误处理**: 完善的异常处理和降级机制

---

**文档版本**: v2.0
**最后更新**: 2024-01-21
**状态**: 需求发布
