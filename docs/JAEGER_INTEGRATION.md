# Enhanced OpenTelemetry Java Agent with Jaeger Integration

## Overview

The enhanced agent has been upgraded to support **J<PERSON>ger integration via OTLP (OpenTelemetry Protocol)**, providing production-ready distributed tracing capabilities. The agent now exports traces to <PERSON><PERSON><PERSON> for visualization and analysis while maintaining the original console output for development.

## New Features

### 🎯 Jaeger Integration
- **OTLP HTTP Exporter**: Sends traces to <PERSON><PERSON><PERSON> using OpenTelemetry Protocol
- **Batch Processing**: Efficient span batching for improved performance
- **Configuration Management**: Flexible configuration via environment variables
- **Production Ready**: Proper resource management and shutdown hooks

### 📊 Enhanced Tracing
- **Accurate Timing**: Proper Unix nanosecond timestamps for OpenTelemetry compatibility
- **Span Relationships**: Correct parent-child relationships between HTTP and database spans
- **OpenTelemetry Standards**: Compliant with OpenTelemetry semantic conventions

## Architecture

```
Application → Agent → BatchSpanProcessor → OtlpSpanExporter → Jaeger
                                      ↘
                                       ConsoleSpanExporter
```

### Key Components

1. **Agent.java**: Enhanced entry point with OTLP initialization
2. **OtlpSpanExporter**: HTTP client for sending traces to Jaeger
3. **BatchSpanProcessor**: Batches spans for efficient export
4. **AgentConfig**: Centralized configuration management
5. **SimpleSpan**: Enhanced span implementation with proper timing

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OTEL_SERVICE_NAME` | `sample-app` | Service name in traces |
| `OTEL_EXPORTER_OTLP_TRACES_ENDPOINT` | `http://localhost:4318/v1/traces` | Jaeger OTLP endpoint |
| `OTEL_EXPORTER_OTLP_ENABLED` | `true` | Enable/disable OTLP export |
| `OTEL_EXPORTER_CONSOLE_ENABLED` | `true` | Enable/disable console export |
| `OTEL_BSP_MAX_EXPORT_BATCH_SIZE` | `512` | Maximum spans per batch |
| `OTEL_BSP_SCHEDULE_DELAY` | `5000` | Batch export delay (ms) |
| `OTEL_BSP_EXPORT_TIMEOUT` | `30000` | Export timeout (ms) |
| `OTEL_BSP_MAX_QUEUE_SIZE` | `2048` | Maximum queue size |

### System Properties

You can also use system properties with the `otel.` prefix:
- `-Dotel.service.name=my-service`
- `-Dotel.exporter.otlp.traces.endpoint=http://jaeger:4318/v1/traces`

## Getting Started

### 1. Start Infrastructure

```bash
# Start PostgreSQL and Jaeger
docker-compose up -d

# Verify Jaeger is running
curl http://localhost:16686
```

### 2. Build the Enhanced Agent

```bash
# Build the agent with new dependencies
./gradlew :agent:jar

# Build the sample application
./gradlew :sample:bootJar
```

### 3. Run with Jaeger Integration

```bash
# Run with default configuration (Jaeger + Console)
java -javaagent:agent/build/libs/agent-1.0.0.jar \\
     -jar sample/build/libs/sample-1.0.0.jar

# Run with custom configuration
java -javaagent:agent/build/libs/agent-1.0.0.jar \\
     -Dotel.service.name=my-app \\
     -Dotel.exporter.otlp.traces.endpoint=http://remote-jaeger:4318/v1/traces \\
     -jar sample/build/libs/sample-1.0.0.jar
```

### 4. Generate Traces

```bash
# Test HTTP + Database tracing
curl -X GET http://localhost:8080/api/auth/users

# Test complex transaction
curl -X POST http://localhost:8080/api/auth/register \\
     -H "Content-Type: application/json" \\
     -d '{"username":"testuser","email":"<EMAIL>","password":"test123"}'
```

### 5. View Traces in Jaeger

1. Open Jaeger UI: http://localhost:16686
2. Select service: `sample-app`
3. Click "Find Traces"
4. Explore the distributed traces with HTTP → Database span relationships

## Features in Detail

### Distributed Tracing

The agent creates a complete trace for each HTTP request:

```
HTTP Request (Server Span)
├── Database Query 1 (Client Span)
├── Database Query 2 (Client Span)
└── Database Insert (Client Span)
```

### Span Attributes

**HTTP Spans**:
- `http.method`: HTTP method (GET, POST, etc.)
- `http.url`: Request path
- `http.status_code`: Response status
- `component`: "spring-web"

**Database Spans**:
- `db.system`: "postgresql"
- `db.statement`: SQL query (parameterized)
- `db.operation`: SQL operation type
- `component`: "jdbc"

### Performance Optimizations

1. **Batch Processing**: Spans are collected and sent in batches (default: 512 spans every 5 seconds)
2. **Asynchronous Export**: Network operations don't block application threads
3. **Efficient ID Generation**: Fast UUID generation for trace and span IDs
4. **Resource Management**: Proper cleanup via shutdown hooks

## Monitoring and Observability

### Console Output

The agent provides detailed console output for monitoring:

```
[CONFIG] OpenTelemetry Agent Configuration
[CONFIG] Service Name: sample-app
[CONFIG] OTLP Enabled: true
[CONFIG] OTLP Endpoint: http://localhost:4318/v1/traces
[BATCH-PROCESSOR] Initialized with batchSize=512, delay=5000ms
[OTLP-SUCCESS] Exported span to Jaeger: traceId=abc123 spanId=def456
[OTLP-BATCH-SUCCESS] Exported 10 spans to Jaeger
```

### Error Handling

The agent gracefully handles various error conditions:

```
[OTLP-ERROR] Failed to export span: HTTP 500 - Internal Server Error
[BATCH-PROCESSOR] Span queue full, dropping span: getUserById
[CONFIG-WARN] OTLP is enabled but no endpoint is configured
```

## Troubleshooting

### Common Issues

1. **No traces in Jaeger**
   - Check Jaeger is running: `docker ps | grep jaeger`
   - Verify endpoint: `curl http://localhost:4318/v1/traces`
   - Check agent logs for export errors

2. **High memory usage**
   - Reduce batch size: `-Dotel.bsp.max.export.batch.size=256`
   - Reduce queue size: `-Dotel.bsp.max.queue.size=1024`

3. **Network timeouts**
   - Increase export timeout: `-Dotel.bsp.export.timeout=60000`
   - Check network connectivity to Jaeger

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
java -javaagent:agent/build/libs/agent-1.0.0.jar \\
     -Dotel.exporter.console.enabled=true \\
     -jar sample/build/libs/sample-1.0.0.jar
```

## Production Deployment

### Docker Deployment

```dockerfile
FROM openjdk:8-jre-slim

COPY agent/build/libs/agent-1.0.0.jar /app/agent.jar
COPY sample/build/libs/sample-1.0.0.jar /app/app.jar

ENV OTEL_SERVICE_NAME=production-app
ENV OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://jaeger-collector:4318/v1/traces

ENTRYPOINT ["java", "-javaagent:/app/agent.jar", "-jar", "/app/app.jar"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sample-app
spec:
  template:
    spec:
      containers:
      - name: app
        image: sample-app:latest
        env:
        - name: OTEL_SERVICE_NAME
          value: "sample-app"
        - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
          value: "http://jaeger-collector:4318/v1/traces"
        - name: OTEL_BSP_SCHEDULE_DELAY
          value: "2000"  # More frequent exports in production
```

### Performance Recommendations

For production environments:

- **Batch Size**: 256-512 spans
- **Export Delay**: 2-5 seconds
- **Queue Size**: 1024-2048 spans
- **Export Timeout**: 30-60 seconds

## Comparison with Standard OpenTelemetry

| Feature | This Agent | Standard OTel Java Agent |
|---------|------------|--------------------------|
| **Size** | ~10MB | ~40MB |
| **Instrumentation** | HTTP + JDBC only | 100+ libraries |
| **Performance** | Minimal overhead | Higher overhead |
| **Configuration** | Simple | Complex |
| **Dependencies** | Minimal | Extensive |

## Future Enhancements

Possible improvements for future versions:

1. **gRPC Export**: Support for OTLP over gRPC
2. **Sampling**: Configurable sampling strategies
3. **More Instrumentations**: Redis, MongoDB, etc.
4. **Metrics Support**: Basic metrics collection
5. **Custom Exporters**: Support for other backends

## Support

For issues and questions:

1. Check the console output for error messages
2. Verify Jaeger connectivity
3. Review configuration settings
4. Test with minimal setup first

---

**Note**: This enhanced agent maintains full backward compatibility. Existing console-only usage continues to work without changes.
