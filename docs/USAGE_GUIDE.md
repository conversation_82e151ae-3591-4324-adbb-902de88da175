# OpenTelemetry Minimal Java Agent 使用指南

## 快速开始

### 1. 环境准备

```bash
# 检查 Java 版本 (支持 JDK 1.8+)
java -version

# 检查 Docker (用于数据库)
docker --version

# 克隆项目
git clone <repository-url>
cd acadia
```

### 2. 一键启动 (推荐)

```bash
# 启动数据库
docker-compose up -d

# 自动构建并启动应用
./run-with-agent.sh
```

启动成功后会看到：
```
========================================
[Agent] OpenTelemetry Minimal Agent Starting...
[Agent] Version: 1.0.0
[Agent] Based on OpenTelemetry Standards
========================================
[Agent] Installed instrumentations:
[Agent] - Spring Controller HTTP tracing
[Agent] - JDBC PreparedStatement database tracing
[Agent] ✅ OpenTelemetry Minimal Agent Started Successfully
```

## API 测试

### 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "password123"
  }'
```

### 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

### 获取用户列表
```bash
curl http://localhost:8080/api/auth/users
```

## 追踪数据解读

每次 API 请求会在控制台输出 OpenTelemetry 格式的追踪数据：

### HTTP 请求 Span (根 Span)
```json
{
  "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
  "spanId": "00f067aa0ba902b7",
  "parentSpanId": "",
  "name": "GET /api/users",
  "kind": "SPAN_KIND_SERVER",
  "attributes": [
    {"key": "http.method", "value": "GET"},
    {"key": "http.url", "value": "/api/users"},
    {"key": "http.status_code", "value": "200"}
  ]
}
```

### 数据库操作 Span (子 Span)
```json
{
  "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
  "spanId": "b9c7c989f97918e1", 
  "parentSpanId": "00f067aa0ba902b7",
  "name": "SELECT users",
  "kind": "SPAN_KIND_CLIENT",
  "attributes": [
    {"key": "db.system", "value": "postgresql"},
    {"key": "db.statement", "value": "SELECT * FROM users"},
    {"key": "component", "value": "jdbc"}
  ]
}
```

## 手动构建和部署

### 构建 Agent
```bash
./gradlew :agent:jar
```
产物：`agent/build/libs/agent-1.0.0.jar` (约 6MB)

### 构建示例应用
```bash
./gradlew :sample:bootJar
```
产物：`sample/build/libs/sample-1.0.0.jar`

### 启动应用
```bash
java -javaagent:agent/build/libs/agent-1.0.0.jar \
     -jar sample/build/libs/sample-1.0.0.jar
```

## 故障排查

### Agent 加载失败
- 检查 JAR 文件存在：`ls -la agent/build/libs/agent-1.0.0.jar`
- 查看 Agent 启动日志是否有错误信息

### 追踪数据缺失
- 确认请求到达了被拦截的端点 (`@RestController` 或 `@Controller`)
- 检查数据库操作是否使用了 `PreparedStatement`

### 构建问题
- Java 版本要求 1.8+
- Gradle 版本要求 6.0+

### 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps

# 重启数据库
docker-compose restart postgres
```

## 项目结构详解

```
acadia/
├── agent/                    # OpenTelemetry Agent 实现
│   ├── src/main/java/ai/servicewall/agent/
│   │   ├── Agent.java                     # 主入口，ByteBuddy 配置
│   │   ├── context/TraceContext.java      # ThreadLocal 上下文管理
│   │   ├── span/SimpleSpan.java           # Span 实现
│   │   ├── exporter/ConsoleSpanExporter.java  # JSON 输出
│   │   └── instrumentation/               # 字节码插桩
│   │       ├── SpringControllerInstrumentation.java
│   │       └── JdbcInstrumentation.java
│   └── build.gradle.kts              # Agent 构建配置
├── sample/                   # Spring Boot 示例应用
├── docker-compose.yml        # PostgreSQL 环境
└── run-with-agent.sh        # 一键启动脚本
```

## OpenTelemetry 标准映射

| 概念 | 实现 | 说明 |
|---|---|---|
| TracerProvider | Agent.java 全局管理 | 创建 Tracer 实例 |
| Tracer | 内置在 Span 创建中 | 负责 Span 生命周期 |
| Span | SimpleSpan.java | 操作执行单元 |
| SpanContext | TraceContext.java | traceId/spanId 传递 |
| Context | ThreadLocal 存储 | 线程内上下文传播 |
| SpanExporter | ConsoleSpanExporter.java | OTLP 格式输出 |

## 扩展开发

### 添加新的插桩
1. 在 `instrumentation/` 创建新拦截器
2. 实现 `@Advice.OnMethodEnter/Exit` 
3. 在 `Agent.java` 注册 AgentBuilder 转换器

### 自定义输出格式
修改 `ConsoleSpanExporter.java` 中的 JSON 序列化逻辑

### 支持更多框架
参考现有拦截器模式，添加对应的 ByteBuddy 插桩逻辑