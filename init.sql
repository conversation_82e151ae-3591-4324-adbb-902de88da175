-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on username and email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Insert some initial test data
INSERT INTO users (username, email, password, role) VALUES
    ('admin', '<EMAIL>', 'password123', 'ADMIN'),
    ('user', '<EMAIL>', 'password456', 'USER'),
    ('test', '<EMAIL>', 'test123', 'USER')
ON CONFLICT (username) DO NOTHING;
