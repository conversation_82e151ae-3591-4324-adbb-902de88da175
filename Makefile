# Makefile for Java Data Sanitization Project (macOS/zsh)
# Supports both Apple Silicon (ARM64) and Intel (AMD64) architectures

SHELL := /bin/zsh
.SHELLFLAGS := -euo pipefail -c

# Environment variables from .env file
-include .env
export

# Default values
POSTGRES_DB ?= acadia
POSTGRES_USER ?= postgres
POSTGRES_PASSWORD ?= postgres
POSTGRES_PORT ?= 5432

# Project paths
PROJECT_ROOT := $(shell pwd)
AGENT_JAR := $(PROJECT_ROOT)/agent/build/libs/agent-1.0.0.jar
SAMPLE_JAR := $(PROJECT_ROOT)/sample/build/libs/sample-1.0.0.jar

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target: show help
.PHONY: help
help:
	@echo "${GREEN}Java Data Sanitization Project - Makefile Commands${NC}"
	@echo ""
	@echo "Available targets:"
	@echo "  ${YELLOW}help${NC}           - Show this help message (default)"
	@echo "  ${YELLOW}build${NC}          - Build the entire project with Gradle"
	@echo "  ${YELLOW}clean${NC}          - Clean all build artifacts"
	@echo "  ${YELLOW}test${NC}           - Run unit tests"
	@echo "  ${YELLOW}db-up${NC}          - Start PostgreSQL and Jaeger containers"
	@echo "  ${YELLOW}wait-db${NC}        - Wait for database to be ready"
	@echo "  ${YELLOW}db-down${NC}        - Stop and remove all containers and volumes"
	@echo "  ${YELLOW}run-test-app${NC}   - Run the sample app with agent instrumentation"
	@echo "  ${YELLOW}e2e${NC}            - Run end-to-end tests (register users and verify)"
	@echo "  ${YELLOW}logs${NC}           - Show application logs"
	@echo ""
	@echo "Quick start:"
	@echo "  make db-up wait-db build run-test-app e2e"

# Build the entire project
.PHONY: build
build:
	@echo "${GREEN}Building project...${NC}"
	@./gradlew clean build
	@echo "${GREEN}Build completed successfully${NC}"

# Clean build artifacts
.PHONY: clean
clean:
	@echo "${YELLOW}Cleaning build artifacts...${NC}"
	@./gradlew clean
	@rm -f nohup.out /tmp/sample-app.log
	@echo "${GREEN}Clean completed${NC}"

# Run unit tests
.PHONY: test
test:
	@echo "${GREEN}Running tests...${NC}"
	@./gradlew test
	@echo "${GREEN}Tests completed${NC}"

# Start database and Jaeger containers
.PHONY: db-up
db-up:
	@echo "${GREEN}Starting PostgreSQL and Jaeger containers...${NC}"
	@if [ -f .env ]; then \
		export $$(cat .env | grep -v '^#' | xargs) && \
		docker-compose up -d; \
	else \
		docker-compose up -d; \
	fi
	@echo "${GREEN}Containers started${NC}"

# Wait for database to be ready
.PHONY: wait-db
wait-db:
	@echo "${YELLOW}Waiting for PostgreSQL to be ready...${NC}"
	@until docker exec postgres_db pg_isready -U $(POSTGRES_USER) -d $(POSTGRES_DB) >/dev/null 2>&1; do \
		echo -n "."; \
		sleep 1; \
	done
	@echo ""
	@echo "${GREEN}PostgreSQL is ready${NC}"

# Stop and remove containers and volumes
.PHONY: db-down
db-down:
	@echo "${RED}Stopping and removing containers...${NC}"
	@docker-compose down -v
	@echo "${GREEN}Containers removed${NC}"

# Run the sample application with agent
.PHONY: run-test-app
run-test-app:
	@echo "${GREEN}Starting sample application with agent...${NC}"
	@# Kill any existing instance
	@pkill -f "sample-1.0.0.jar" 2>/dev/null || true
	@sleep 1
	@# Start the application
	@nohup java -Xms256m -Xmx512m \
		-Dotel.service.name=sample-app \
		-Dotel.exporter.otlp.traces.endpoint=http://localhost:4318/v1/traces \
		-Dotel.exporter.otlp.enabled=true \
		-Dotel.exporter.console.enabled=true \
		-javaagent:$(AGENT_JAR) \
		-jar $(SAMPLE_JAR) > /tmp/sample-app.log 2>&1 &
	@echo "${GREEN}Application started. Waiting for startup...${NC}"
	@sleep 8
	@echo "${GREEN}Application should be ready at http://localhost:8080${NC}"

# End-to-end test: register users and verify
.PHONY: e2e
e2e:
	@echo "${GREEN}Running end-to-end tests...${NC}"
	@echo "${YELLOW}Step 1: Checking if app is running...${NC}"
	@curl -f -s http://localhost:8080/api/auth/users >/dev/null || (echo "${RED}App not running!${NC}" && exit 1)
	@echo "${GREEN}App is running${NC}"
	
	@echo "${YELLOW}Step 2: Registering test users...${NC}"
	@# Register user 1
	@curl -s -X POST http://localhost:8080/api/auth/register \
		-H 'Content-Type: application/json' \
		-d '{"username":"testuser1","email":"<EMAIL>","password":"pass123","role":"USER"}' \
		| jq -r '.message' || true
	
	@# Register user 2
	@curl -s -X POST http://localhost:8080/api/auth/register \
		-H 'Content-Type: application/json' \
		-d '{"username":"testuser2","email":"<EMAIL>","password":"pass456","role":"USER"}' \
		| jq -r '.message' || true
	
	@# Register user 3
	@curl -s -X POST http://localhost:8080/api/auth/register \
		-H 'Content-Type: application/json' \
		-d '{"username":"testuser3","email":"<EMAIL>","password":"pass789","role":"ADMIN"}' \
		| jq -r '.message' || true
	
	@echo "${YELLOW}Step 3: Fetching user list...${NC}"
	@USER_COUNT=$$(curl -s http://localhost:8080/api/auth/users | jq '.users | length'); \
	if [ "$$USER_COUNT" -ge 3 ]; then \
		echo "${GREEN}✓ Test passed: Found $$USER_COUNT users (expected >= 3)${NC}"; \
		echo "${GREEN}✓ Agent is working correctly - users are visible to the application${NC}"; \
	else \
		echo "${RED}✗ Test failed: Found $$USER_COUNT users (expected >= 3)${NC}"; \
		echo "${RED}✗ Agent may be consuming ResultSet data${NC}"; \
		exit 1; \
	fi
	
	@echo "${YELLOW}Step 4: Checking trace data...${NC}"
	@sleep 2
	@if curl -s "http://localhost:16686/api/services" | jq -e '.data[] | select(. == "sample-app")' >/dev/null; then \
		echo "${GREEN}✓ Traces are being sent to Jaeger${NC}"; \
	else \
		echo "${YELLOW}⚠ Warning: No traces found in Jaeger yet${NC}"; \
	fi

# Show application logs
.PHONY: logs
logs:
	@echo "${GREEN}Showing application logs...${NC}"
	@if [ -f /tmp/sample-app.log ]; then \
		tail -f /tmp/sample-app.log; \
	else \
		echo "${RED}No log file found. Is the application running?${NC}"; \
	fi

# Combined target for full setup and test
.PHONY: all
all: db-up wait-db build run-test-app e2e
	@echo "${GREEN}All tasks completed successfully!${NC}"

# Default target
.DEFAULT_GOAL := help
