services:
  postgres:
    image: postgres:17-alpine
    container_name: postgres_db
    environment:
      POSTGRES_DB: "${POSTGRES_DB:-acadia}"
      POSTGRES_USER: "${POSTGRES_USER:-postgres}"
      POSTGRES_PASSWORD: "${POSTGRES_PASSWORD:-postgres}"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - acadia

  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger
    environment:
      COLLECTOR_OTLP_ENABLED: "true"
      SPAN_STORAGE_TYPE: "memory"
    ports:
      - "16686:16686"    # Jaeger UI
      - "14268:14268"    # HTTP collector (for OTLP traces)
      - "14250:14250"    # gRPC collector
      - "4317:4317"      # OTLP gRPC receiver
      - "4318:4318"      # OTLP HTTP receiver
      - "6831:6831/udp"  # UDP agent endpoint
      - "6832:6832/udp"  # UDP agent endpoint
    command: [
      "--collector.otlp.grpc.host-port=:4317",
      "--collector.otlp.http.host-port=:4318"
    ]
    networks:
      - acadia

volumes:
  postgres-data:
    driver: local

networks:
  acadia:
    driver: bridge
