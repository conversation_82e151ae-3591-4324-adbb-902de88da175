package ai.servicewall.agent.config;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Configuration for database result data capturing and data sanitization
 * Supports configurable limits, table exclusion, and data sanitization rules
 */
public class DataCaptureConfig {

    // Default configuration values
    private static final int DEFAULT_MAX_ROWS = 10;
    private static final boolean DEFAULT_CAPTURE_ENABLED = true;
    private static final boolean DEFAULT_SANITIZATION_ENABLED = true;
    private static final String DEFAULT_MASK_STRING = "***";

    // Singleton instance
    private static volatile DataCaptureConfig instance;
    private static final Object lock = new Object();

    // Configuration properties
    private final boolean captureEnabled;
    private final int maxRows;
    private final Set<String> excludedTables;

    // Data sanitization properties
    private final boolean sanitizationEnabled;
    private final String maskString;
    private final Map<String, SanitizationRule> sensitiveColumns;
    private final Set<Pattern> sensitivePatterns;

    private DataCaptureConfig() {
        // Load from system properties or use defaults
        this.captureEnabled = Boolean.parseBoolean(
            System.getProperty("agent.data.capture.enabled", String.valueOf(DEFAULT_CAPTURE_ENABLED)));

        this.maxRows = Integer.parseInt(
            System.getProperty("agent.data.capture.maxRows", String.valueOf(DEFAULT_MAX_ROWS)));

        // Initialize excluded tables
        String excludedTablesStr = System.getProperty("agent.data.capture.excludedTables", "");
        this.excludedTables = new HashSet<>();
        if (!excludedTablesStr.trim().isEmpty()) {
            for (String table : excludedTablesStr.split(",")) {
                this.excludedTables.add(table.trim().toLowerCase());
            }
        }

        // Initialize data sanitization settings
        this.sanitizationEnabled = Boolean.parseBoolean(
            System.getProperty("agent.data.sanitization.enabled", String.valueOf(DEFAULT_SANITIZATION_ENABLED)));

        this.maskString = System.getProperty("agent.data.sanitization.maskString", DEFAULT_MASK_STRING);

        // Initialize sensitive columns with default rules
        this.sensitiveColumns = new HashMap<>();
        this.sensitivePatterns = new HashSet<>();
        initializeDefaultSanitizationRules();
    }

    public static DataCaptureConfig getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new DataCaptureConfig();
                }
            }
        }
        return instance;
    }

    // Getters
    public boolean isCaptureEnabled() {
        return captureEnabled;
    }

    public int getMaxRows() {
        return maxRows;
    }

    public Set<String> getExcludedTables() {
        return new HashSet<>(excludedTables);
    }

    /**
     * Check if a table should be excluded from data capture
     */
    public boolean isExcludedTable(String tableName) {
        if (tableName == null) {
            return false;
        }
        return excludedTables.contains(tableName.toLowerCase());
    }

    /**
     * Print current configuration
     */
    public void printConfig() {
        System.out.println("[DataCapture] Configuration:");
        System.out.println("[DataCapture]   Capture Enabled: " + captureEnabled);
        System.out.println("[DataCapture]   Max Rows: " + maxRows);
        System.out.println("[DataCapture]   Excluded Tables: " + excludedTables);
        System.out.println("[DataCapture]   Sanitization Enabled: " + sanitizationEnabled);
        System.out.println("[DataCapture]   Mask String: " + maskString);
        System.out.println("[DataCapture]   Sensitive Columns: " + sensitiveColumns.keySet());
    }

    /**
     * Initialize default data sanitization rules
     */
    private void initializeDefaultSanitizationRules() {
        // Password fields - complete masking
        addSensitiveColumn("password", SanitizationType.MASK);
        addSensitiveColumn("passwd", SanitizationType.MASK);
        addSensitiveColumn("pwd", SanitizationType.MASK);

        // Email fields - partial masking (show domain)
        addSensitiveColumn("email", SanitizationType.EMAIL_MASK);
        addSensitiveColumn("email_address", SanitizationType.EMAIL_MASK);

        // Phone fields - partial masking
        addSensitiveColumn("phone", SanitizationType.PHONE_MASK);
        addSensitiveColumn("telephone", SanitizationType.PHONE_MASK);
        addSensitiveColumn("mobile", SanitizationType.PHONE_MASK);

        // ID card, credit card etc - hash
        addSensitiveColumn("id_card", SanitizationType.HASH);
        addSensitiveColumn("credit_card", SanitizationType.HASH);
        addSensitiveColumn("ssn", SanitizationType.HASH);

        // Add regex patterns for automatic detection
        sensitivePatterns.add(Pattern.compile(".*password.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*passwd.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*email.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*phone.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*mobile.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*secret.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*token.*", Pattern.CASE_INSENSITIVE));
        sensitivePatterns.add(Pattern.compile(".*key.*", Pattern.CASE_INSENSITIVE));
    }

    /**
     * Add a sensitive column with sanitization rule
     */
    private void addSensitiveColumn(String columnName, SanitizationType type) {
        sensitiveColumns.put(columnName.toLowerCase(), new SanitizationRule(type, maskString));
    }

    // Additional getters for sanitization
    public boolean isSanitizationEnabled() {
        return sanitizationEnabled;
    }

    public String getMaskString() {
        return maskString;
    }

    public SanitizationRule getSanitizationRule(String columnName) {
        if (!sanitizationEnabled || columnName == null) {
            return null;
        }

        String lowerColumnName = columnName.toLowerCase();

        // First check exact match
        SanitizationRule rule = sensitiveColumns.get(lowerColumnName);
        if (rule != null) {
            return rule;
        }

        // Then check pattern matches
        for (Pattern pattern : sensitivePatterns) {
            if (pattern.matcher(lowerColumnName).matches()) {
                return new SanitizationRule(SanitizationType.MASK, maskString);
            }
        }

        return null;
    }

    public boolean isSensitiveColumn(String columnName) {
        return getSanitizationRule(columnName) != null;
    }

    /**
     * Validate configuration values
     */
    public void validate() {
        if (maxRows < 0) {
            throw new IllegalArgumentException("maxRows must be >= 0");
        }
        if (maskString == null || maskString.isEmpty()) {
            throw new IllegalArgumentException("maskString cannot be null or empty");
        }
    }

    /**
     * Sanitization rule for a column
     */
    public static class SanitizationRule {
        private final SanitizationType type;
        private final String maskValue;

        public SanitizationRule(SanitizationType type, String maskValue) {
            this.type = type;
            this.maskValue = maskValue;
        }

        public SanitizationType getType() {
            return type;
        }

        public String getMaskValue() {
            return maskValue;
        }
    }

    /**
     * Types of data sanitization
     */
    public enum SanitizationType {
        MASK,           // Complete masking: "password123" -> "***"
        PARTIAL_MASK,   // Partial masking: "john123" -> "joh***"
        EMAIL_MASK,     // Email masking: "<EMAIL>" -> "u***@example.com"
        PHONE_MASK,     // Phone masking: "13812345678" -> "138****5678"
        HASH,           // Hash value: "password123" -> "sha256:abcd1234..."
        NONE            // No sanitization
    }
}
