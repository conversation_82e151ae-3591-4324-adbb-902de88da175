package ai.servicewall.agent.config;

/**
 * Agent Configuration System
 * Manages OTLP endpoint settings and other agent configuration parameters
 */
public class AgentConfig {

    // Default configuration values
    private static final String DEFAULT_SERVICE_NAME = "sample-app";
    private static final String DEFAULT_OTLP_ENDPOINT = "http://localhost:4318/v1/traces";
    private static final boolean DEFAULT_OTLP_ENABLED = true;
    private static final boolean DEFAULT_CONSOLE_ENABLED = true;
    private static final int DEFAULT_BATCH_SIZE = 512;
    private static final int DEFAULT_BATCH_DELAY_MS = 5000;
    private static final int DEFAULT_EXPORT_TIMEOUT_MS = 30000;
    private static final int DEFAULT_MAX_QUEUE_SIZE = 2048;

    // Configuration instance
    private static volatile AgentConfig instance;
    private static final Object lock = new Object();

    // Configuration properties
    private final String serviceName;
    private final String otlpEndpoint;
    private final boolean otlpEnabled;
    private final boolean consoleEnabled;
    private final int batchSize;
    private final int batchDelayMs;
    private final int exportTimeoutMs;
    private final int maxQueueSize;

    private AgentConfig() {
        // Service configuration
        this.serviceName = getProperty("otel.service.name",
                                     getEnv("OTEL_SERVICE_NAME", DEFAULT_SERVICE_NAME));

        // OTLP exporter configuration
        this.otlpEndpoint = getProperty("otel.exporter.otlp.traces.endpoint",
                                      getEnv("OTEL_EXPORTER_OTLP_TRACES_ENDPOINT", DEFAULT_OTLP_ENDPOINT));

        this.otlpEnabled = Boolean.parseBoolean(
            getProperty("otel.exporter.otlp.enabled",
                      getEnv("OTEL_EXPORTER_OTLP_ENABLED", String.valueOf(DEFAULT_OTLP_ENABLED))));

        // Console exporter configuration
        this.consoleEnabled = Boolean.parseBoolean(
            getProperty("otel.exporter.console.enabled",
                      getEnv("OTEL_EXPORTER_CONSOLE_ENABLED", String.valueOf(DEFAULT_CONSOLE_ENABLED))));

        // Batch processor configuration
        this.batchSize = Integer.parseInt(
            getProperty("otel.bsp.max.export.batch.size",
                      getEnv("OTEL_BSP_MAX_EXPORT_BATCH_SIZE", String.valueOf(DEFAULT_BATCH_SIZE))));

        this.batchDelayMs = Integer.parseInt(
            getProperty("otel.bsp.schedule.delay",
                      getEnv("OTEL_BSP_SCHEDULE_DELAY", String.valueOf(DEFAULT_BATCH_DELAY_MS))));

        this.exportTimeoutMs = Integer.parseInt(
            getProperty("otel.bsp.export.timeout",
                      getEnv("OTEL_BSP_EXPORT_TIMEOUT", String.valueOf(DEFAULT_EXPORT_TIMEOUT_MS))));

        this.maxQueueSize = Integer.parseInt(
            getProperty("otel.bsp.max.queue.size",
                      getEnv("OTEL_BSP_MAX_QUEUE_SIZE", String.valueOf(DEFAULT_MAX_QUEUE_SIZE))));
    }

    /**
     * Get singleton instance of AgentConfig
     */
    public static AgentConfig getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new AgentConfig();
                }
            }
        }
        return instance;
    }

    /**
     * Get property with fallback
     */
    private String getProperty(String key, String defaultValue) {
        return System.getProperty(key, defaultValue);
    }

    /**
     * Get environment variable with fallback
     */
    private String getEnv(String key, String defaultValue) {
        String value = System.getenv(key);
        return value != null ? value : defaultValue;
    }

    // Getters
    public String getServiceName() {
        return serviceName;
    }

    public String getOtlpEndpoint() {
        return otlpEndpoint;
    }

    public boolean isOtlpEnabled() {
        return otlpEnabled;
    }

    public boolean isConsoleEnabled() {
        return consoleEnabled;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public int getBatchDelayMs() {
        return batchDelayMs;
    }

    public int getExportTimeoutMs() {
        return exportTimeoutMs;
    }

    public int getMaxQueueSize() {
        return maxQueueSize;
    }

    /**
     * Print configuration summary
     */
    public void printConfig() {
        System.out.println("========================================");
        System.out.println("[CONFIG] OpenTelemetry Agent Configuration");
        System.out.println("========================================");
        System.out.printf("[CONFIG] Service Name: %s%n", serviceName);
        System.out.printf("[CONFIG] OTLP Enabled: %s%n", otlpEnabled);
        if (otlpEnabled) {
            System.out.printf("[CONFIG] OTLP Endpoint: %s%n", otlpEndpoint);
        }
        System.out.printf("[CONFIG] Console Enabled: %s%n", consoleEnabled);
        System.out.printf("[CONFIG] Batch Size: %d%n", batchSize);
        System.out.printf("[CONFIG] Batch Delay: %dms%n", batchDelayMs);
        System.out.printf("[CONFIG] Export Timeout: %dms%n", exportTimeoutMs);
        System.out.printf("[CONFIG] Max Queue Size: %d%n", maxQueueSize);
        System.out.println("========================================");
    }

    /**
     * Check if tracing is enabled
     */
    public boolean isTracingEnabled() {
        return otlpEnabled || consoleEnabled;
    }

    /**
     * Validate configuration
     */
    public void validate() {
        if (!isTracingEnabled()) {
            System.err.println("[CONFIG-WARN] Both OTLP and Console exporters are disabled. No traces will be exported.");
        }

        if (otlpEnabled && (otlpEndpoint == null || otlpEndpoint.trim().isEmpty())) {
            System.err.println("[CONFIG-WARN] OTLP is enabled but no endpoint is configured.");
        }

        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive: " + batchSize);
        }

        if (batchDelayMs <= 0) {
            throw new IllegalArgumentException("Batch delay must be positive: " + batchDelayMs);
        }

        if (exportTimeoutMs <= 0) {
            throw new IllegalArgumentException("Export timeout must be positive: " + exportTimeoutMs);
        }

        if (maxQueueSize <= 0) {
            throw new IllegalArgumentException("Max queue size must be positive: " + maxQueueSize);
        }
    }
}
