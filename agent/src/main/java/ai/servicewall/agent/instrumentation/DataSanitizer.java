package ai.servicewall.agent.instrumentation;

import java.security.MessageDigest;
import java.util.regex.Pattern;

import ai.servicewall.agent.config.DataCaptureConfig;
import ai.servicewall.agent.config.DataCaptureConfig.SanitizationRule;

/**
 * Comprehensive data sanitization utility for database result data
 * Provides multiple sanitization strategies while preserving data capture functionality
 *
 * IMPORTANT: This class only sanitizes traced/monitored data - it never affects
 * the original data that applications receive from the database.
 */
public class DataSanitizer {

    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[^@]+@[^@]+\\.[^@]+$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^[\\d\\-\\+\\(\\)\\s]{7,20}$");

    /**
     * Sanitize a data value based on column name and configuration
     * This method only affects the traced data, not the original application data
     *
     * @param columnName the name of the database column
     * @param originalValue the original value from the database
     * @param config the data capture configuration
     * @return sanitized value for tracing (original value is unchanged for application)
     */
    public static Object sanitizeValue(String columnName, Object originalValue, DataCaptureConfig config) {
        if (!config.isSanitizationEnabled() || originalValue == null) {
            return originalValue;
        }

        // Get sanitization rule for this column
        SanitizationRule rule = config.getSanitizationRule(columnName);
        if (rule == null) {
            return originalValue; // No sanitization needed
        }

        String stringValue = originalValue.toString();

        switch (rule.getType()) {
            case MASK:
                return applyMask(stringValue, rule.getMaskValue());
            case PARTIAL_MASK:
                return applyPartialMask(stringValue, rule.getMaskValue());
            case EMAIL_MASK:
                return applyEmailMask(stringValue, rule.getMaskValue());
            case PHONE_MASK:
                return applyPhoneMask(stringValue, rule.getMaskValue());
            case HASH:
                return applyHash(stringValue);
            case NONE:
            default:
                return originalValue;
        }
    }

    /**
     * Apply complete masking to a value
     */
    private static String applyMask(String value, String maskValue) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        return maskValue;
    }

    /**
     * Apply partial masking (show first 3 characters, mask the rest)
     */
    private static String applyPartialMask(String value, String maskValue) {
        if (value == null || value.length() <= 3) {
            return maskValue;
        }
        return value.substring(0, 3) + maskValue;
    }

    /**
     * Apply email masking (show first character and domain)
     */
    private static String applyEmailMask(String value, String maskValue) {
        if (value == null || !EMAIL_PATTERN.matcher(value).matches()) {
            return maskValue;
        }

        int atIndex = value.indexOf('@');
        if (atIndex <= 1) {
            return maskValue;
        }

        String localPart = value.substring(0, atIndex);
        String domainPart = value.substring(atIndex);

        return localPart.charAt(0) + maskValue + domainPart;
    }

    /**
     * Apply phone number masking (show first 3 and last 4 digits)
     */
    private static String applyPhoneMask(String value, String maskValue) {
        if (value == null || !PHONE_PATTERN.matcher(value).matches()) {
            return maskValue;
        }

        // Extract only digits
        String digits = value.replaceAll("[^\\d]", "");
        if (digits.length() < 7) {
            return maskValue;
        }

        if (digits.length() <= 7) {
            return digits.substring(0, 3) + maskValue;
        } else {
            return digits.substring(0, 3) + maskValue + digits.substring(digits.length() - 4);
        }
    }

    /**
     * Apply hash to a value for complete anonymization
     */
    private static String applyHash(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }

        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(value.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return "sha256:" + sb.toString().substring(0, 16) + "..."; // Show first 16 chars
        } catch (Exception e) {
            return "hash:error";
        }
    }

    /**
     * Extract table name from SQL statement for exclusion checking
     */
    public static String extractTableName(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return null;
        }

        String upperSql = sql.trim().toUpperCase();

        try {
            if (upperSql.startsWith("SELECT")) {
                // Extract table name from SELECT statement
                // Simple pattern: SELECT ... FROM table_name
                int fromIndex = upperSql.indexOf("FROM");
                if (fromIndex > 0) {
                    String afterFrom = sql.substring(fromIndex + 4).trim();
                    String[] parts = afterFrom.split("\\s+");
                    if (parts.length > 0) {
                        // Remove schema prefix if present (schema.table -> table)
                        String tableName = parts[0];
                        int dotIndex = tableName.lastIndexOf('.');
                        if (dotIndex > 0 && dotIndex < tableName.length() - 1) {
                            tableName = tableName.substring(dotIndex + 1);
                        }
                        return tableName.toLowerCase();
                    }
                }
            } else if (upperSql.startsWith("INSERT")) {
                // Extract table name from INSERT statement
                // Pattern: INSERT INTO table_name
                int intoIndex = upperSql.indexOf("INTO");
                if (intoIndex > 0) {
                    String afterInto = sql.substring(intoIndex + 4).trim();
                    String[] parts = afterInto.split("\\s+");
                    if (parts.length > 0) {
                        String tableName = parts[0];
                        int dotIndex = tableName.lastIndexOf('.');
                        if (dotIndex > 0 && dotIndex < tableName.length() - 1) {
                            tableName = tableName.substring(dotIndex + 1);
                        }
                        return tableName.toLowerCase();
                    }
                }
            } else if (upperSql.startsWith("UPDATE")) {
                // Extract table name from UPDATE statement
                // Pattern: UPDATE table_name SET
                String[] parts = sql.trim().split("\\s+");
                if (parts.length > 1) {
                    String tableName = parts[1];
                    int dotIndex = tableName.lastIndexOf('.');
                    if (dotIndex > 0 && dotIndex < tableName.length() - 1) {
                        tableName = tableName.substring(dotIndex + 1);
                    }
                    return tableName.toLowerCase();
                }
            } else if (upperSql.startsWith("DELETE")) {
                // Extract table name from DELETE statement
                // Pattern: DELETE FROM table_name
                int fromIndex = upperSql.indexOf("FROM");
                if (fromIndex > 0) {
                    String afterFrom = sql.substring(fromIndex + 4).trim();
                    String[] parts = afterFrom.split("\\s+");
                    if (parts.length > 0) {
                        String tableName = parts[0];
                        int dotIndex = tableName.lastIndexOf('.');
                        if (dotIndex > 0 && dotIndex < tableName.length() - 1) {
                            tableName = tableName.substring(dotIndex + 1);
                        }
                        return tableName.toLowerCase();
                    }
                }
            }
        } catch (Exception e) {
            // If parsing fails, return null
            return null;
        }

        return null;
    }

    /**
     * Check if data capture should be skipped for this SQL statement
     */
    public static boolean shouldSkipDataCapture(String sql, DataCaptureConfig config) {
        if (!config.isCaptureEnabled()) {
            return true;
        }

        if (sql == null || sql.trim().isEmpty()) {
            return true;
        }

        String tableName = extractTableName(sql);
        if (tableName != null && config.isExcludedTable(tableName)) {
            return true;
        }

        return false;
    }
}
