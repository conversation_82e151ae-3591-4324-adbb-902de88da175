package ai.servicewall.agent.instrumentation;

import ai.servicewall.agent.config.DataCaptureConfig;
import net.bytebuddy.asm.Advice;

/**
 * ByteBuddy instrumentation for User entity setters
 * Implements zero-intrusion data sanitization at the entity level
 */
public class UserEntityInstrumentation {

    // 防止递归调用的ThreadLocal标记
    public static final ThreadLocal<Boolean> isProcessing = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    /**
     * Intercept User entity setter methods for data sanitization
     */
    @Advice.OnMethodEnter
    public static void onSetterEnter(
            @Advice.This Object entity,
            @Advice.Origin String methodName,
            @Advice.AllArguments Object[] args) {

        // 防止递归调用
        if (isProcessing.get()) {
            return;
        }

        try {
            isProcessing.set(true);

            if (args != null && args.length > 0 && args[0] != null) {
                Object originalValue = args[0];
                String fieldName = extractFieldNameFromSetter(methodName);

                System.err.println("[Agent-Entity] 🔍 OnEnter - Processing setter: method=" + methodName +
                                 " fieldName=" + fieldName + " originalValue='" + originalValue + "'");

                if (fieldName != null) {
                    DataCaptureConfig config = DataCaptureConfig.getInstance();

                    // Check if this field should be sanitized
                    if (config.isSanitizationEnabled() && config.isSensitiveColumn(fieldName)) {
                        Object sanitizedValue = DataSanitizer.sanitizeValue(fieldName, originalValue, config);

                        System.err.println("[Agent-Entity] 🛡️ CRITICAL - SANITIZATION APPLIED for '" + fieldName +
                                         "': ORIGINAL='" + originalValue + "' -> SANITIZED='" + sanitizedValue + "'");

                        // Replace the argument with sanitized value
                        args[0] = sanitizedValue;

                        System.err.println("[Agent-Entity] 🔄 CRITICAL - REPLACED SETTER ARGUMENT: '" + sanitizedValue + "'");
                        System.err.flush();
                    } else {
                        System.err.println("[Agent-Entity] ℹ️ Field '" + fieldName + "' does not require sanitization");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("[Agent-Entity] ❌ Error in setter instrumentation: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isProcessing.set(false);
        }
    }

    /**
     * Extract field name from setter method name
     * Examples:
     * - "setEmail" -> "email"
     * - "setPassword" -> "password"
     * - "setUsername" -> "username"
     */
    private static String extractFieldNameFromSetter(String methodName) {
        if (methodName == null || !methodName.startsWith("set") || methodName.length() <= 3) {
            return null;
        }

        // Remove "set" prefix and convert first character to lowercase
        String fieldName = methodName.substring(3);
        if (fieldName.length() > 0) {
            fieldName = Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
        }

        return fieldName;
    }
}
