package ai.servicewall.agent.instrumentation;

import java.sql.ResultSet;
import net.bytebuddy.asm.Advice;

/**
 * ResultSet.close() 方法拦截器
 * 专门用于清理 JdbcSpanStorage
 */
public class ResultSetCloseInstrumentation {

    /**
     * Intercept ResultSet.close() to cleanup JdbcSpanStorage
     */
    @Advice.OnMethodExit
    public static void onResultSetClose(@Advice.This ResultSet resultSet) {
        
        try {
            System.err.println("[Agent-ResultSetClose] 📤 ResultSet.close() called, unregistering from JdbcSpanStorage");
            JdbcSpanStorage.unregisterResultSet(resultSet);
        } catch (Exception e) {
            System.err.println("[Agent-ResultSetClose] Error in ResultSet close handler: " + e.getMessage());
        }
    }
}