package ai.servicewall.agent.instrumentation;

import ai.servicewall.agent.config.DataCaptureConfig;
import net.bytebuddy.asm.Advice;
import java.lang.reflect.Field;

/**
 * Hibernate TwoPhaseLoad instrumentation for data sanitization
 * Intercepts Hibernate entity loading to sanitize sensitive data at the entity
 * level
 */
public class HibernateInstrumentation {

    // 防止递归调用的ThreadLocal标记 - 必须是public以支持bootstrap classloader访问
    public static final ThreadLocal<Boolean> isProcessing = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    /**
     * Intercept Hibernate's TwoPhaseLoad methods to discover and potentially
     * sanitize sensitive data
     * This is where Hibernate processes entity property values from ResultSet data
     */
    @Advice.OnMethodEnter
    public static void onTwoPhaseLoadMethod(
            @Advice.Origin String methodName,
            @Advice.AllArguments Object[] args) {

        // 防止递归调用
        if (isProcessing.get()) {
            return;
        }

        try {
            isProcessing.set(true);

            // Log all TwoPhaseLoad method calls to understand the flow
            System.err.println("[Agent-Hibernate] 🔍 TwoPhaseLoad method called: " + methodName);
            if (args != null && args.length > 0) {
                System.err.println("[Agent-Hibernate] 📋 Arguments count: " + args.length);
                for (int i = 0; i < Math.min(args.length, 5); i++) {
                    if (args[i] != null) {
                        System.err.println("[Agent-Hibernate] 📋 Arg[" + i + "]: " +
                                args[i].getClass().getSimpleName() + " = " + args[i]);
                    }
                }
            }
            System.err.flush();

            // Try to intercept property setting methods
            if (methodName.contains("Property") || methodName.contains("Value") || methodName.contains("attribute")) {
                attemptPropertySanitization(methodName, args);
            }

        } catch (Exception e) {
            System.err.println("[Agent-Hibernate] Error in TwoPhaseLoad method interception: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isProcessing.set(false);
        }
    }

    /**
     * Attempt to sanitize property values in TwoPhaseLoad method calls
     */
    private static void attemptPropertySanitization(String methodName, Object[] args) {
        if (args == null || args.length < 2) {
            return;
        }

        // Look for entity, property name, and value in the arguments
        Object entity = null;
        Object value = null;
        String propertyName = null;

        // Try to extract meaningful arguments
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg == null)
                continue;

            // Look for entity object (usually first argument)
            if (i == 0 && !arg.getClass().getName().startsWith("java.")) {
                entity = arg;
            }
            // Look for property name (String argument)
            else if (arg instanceof String && !arg.toString().isEmpty()) {
                String str = (String) arg;
                if (str.matches("^[a-zA-Z_][a-zA-Z0-9_]*$") && str.length() < 50) {
                    propertyName = str;
                }
            }
            // Look for value (String that's not a property name)
            else if (arg instanceof String && value == null) {
                value = arg;
            }
        }

        if (entity == null || propertyName == null || value == null || !(value instanceof String)) {
            return;
        }

        // Check if this property should be sanitized
        DataCaptureConfig config = DataCaptureConfig.getInstance();
        if (!config.isSanitizationEnabled() || !config.isSensitiveColumn(propertyName)) {
            return;
        }

        // Sanitize the value
        String originalValue = (String) value;
        Object sanitizedValue = DataSanitizer.sanitizeValue(propertyName, originalValue, config);

        System.err.println("[Agent-Hibernate] 🛡️ Attempting to sanitize entity property: " + propertyName +
                " = " + originalValue + " -> " + sanitizedValue);
        System.err.flush();

        // Try to replace the value in the arguments array
        for (int i = 0; i < args.length; i++) {
            if (args[i] == value) {
                args[i] = sanitizedValue;
                System.err.println("[Agent-Hibernate] ✅ Replaced argument[" + i + "] with sanitized value");
                break;
            }
        }

        // Also try to set the property directly on the entity using reflection
        try {
            Field field = findField(entity.getClass(), propertyName);
            if (field != null) {
                field.setAccessible(true);
                field.set(entity, sanitizedValue);
                System.err.println(
                        "[Agent-Hibernate] ✅ Successfully set sanitized value on entity field: " + propertyName);
                System.err.flush();
            }
        } catch (Exception e) {
            System.err.println("[Agent-Hibernate] ⚠️ Could not set field directly: " + e.getMessage());
        }
    }

    /**
     * Find a field in the class hierarchy
     */
    private static Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
}
