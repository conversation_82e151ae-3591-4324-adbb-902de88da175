package ai.servicewall.agent.instrumentation;

import ai.servicewall.agent.span.SimpleSpan;
import java.sql.ResultSet;
import java.util.Collections;
import java.util.Set;
import java.util.WeakHashMap;

/**
 * ThreadLocal storage for active JDBC spans
 * Enhanced version with ResultSet lifecycle tracking
 */
public class JdbcSpanStorage {

    private static final ThreadLocal<SimpleSpan> activeJdbcSpan = new ThreadLocal<>();
    
    // 跟踪活跃的 ResultSet，使用 WeakHashMap 防止内存泄漏
    private static final Set<ResultSet> activeResultSets = Collections.synchronizedSet(
        Collections.newSetFromMap(new WeakHashMap<>()));

    /**
     * Set the active JDBC span for the current thread
     */
    public static void setActiveJdbcSpan(SimpleSpan span) {
        activeJdbcSpan.set(span);
        System.err.println("[JdbcSpanStorage] 设置活跃 JDBC Span: " + 
                         (span != null ? span.getOperationName() : "null"));
    }

    /**
     * Get the active JDBC span for the current thread
     */
    public static SimpleSpan getActiveJdbcSpan() {
        SimpleSpan span = activeJdbcSpan.get();
        System.err.println("[JdbcSpanStorage] 获取活跃 JDBC Span: " + 
                         (span != null ? span.getOperationName() : "null"));
        return span;
    }

    /**
     * Register a ResultSet as active
     */
    public static void registerResultSet(ResultSet resultSet) {
        if (resultSet != null) {
            activeResultSets.add(resultSet);
            System.err.println("[JdbcSpanStorage] 注册活跃 ResultSet: " + resultSet.getClass().getSimpleName());
        }
    }
    
    /**
     * Unregister a ResultSet (when closed or done)
     */
    public static void unregisterResultSet(ResultSet resultSet) {
        if (resultSet != null) {
            boolean removed = activeResultSets.remove(resultSet);
            System.err.println("[JdbcSpanStorage] 注销 ResultSet: " + removed);
            
            // 如果没有活跃的 ResultSet 了，可以清理 JDBC Span
            if (activeResultSets.isEmpty()) {
                System.err.println("[JdbcSpanStorage] 所有 ResultSet 已处理完毕，清理 JDBC Span");
                clearActiveJdbcSpan();
            }
        }
    }

    /**
     * Check if there are active ResultSets
     */
    public static boolean hasActiveResultSets() {
        return !activeResultSets.isEmpty();
    }

    /**
     * Clear the active JDBC span for the current thread
     * Only if no active ResultSets remain
     */
    public static void clearActiveJdbcSpan() {
        SimpleSpan span = activeJdbcSpan.get();
        if (span != null) {
            System.err.println("[JdbcSpanStorage] 清理活跃 JDBC Span: " + span.getOperationName());
            activeJdbcSpan.remove();
        }
    }
    
    /**
     * Force clear for cleanup (use with caution)
     */
    public static void forceClearActiveJdbcSpan() {
        activeJdbcSpan.remove();
        activeResultSets.clear();
        System.err.println("[JdbcSpanStorage] 强制清理所有状态");
    }
}
