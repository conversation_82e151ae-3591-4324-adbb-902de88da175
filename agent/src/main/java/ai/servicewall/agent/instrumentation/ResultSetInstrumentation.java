package ai.servicewall.agent.instrumentation;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;

import ai.servicewall.agent.config.DataCaptureConfig;
import ai.servicewall.agent.span.SimpleSpan;
import ai.servicewall.agent.util.SqlColumnExtractor;
import net.bytebuddy.asm.Advice;

/**
 * ByteBuddy instrumentation for java.sql.ResultSet
 * Implements zero-intrusion data sanitization at the database level
 */
public class ResultSetInstrumentation {

    // 防止递归调用的ThreadLocal标记 - 必须是public以支持bootstrap classloader访问
    public static final ThreadLocal<Boolean> isProcessing = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    // ThreadLocal to store the sanitized value for return value replacement - must be public for bootstrap classloader access
    public static final ThreadLocal<String> sanitizedReturnValue = new ThreadLocal<>();

    /**
     * Intercept getString() methods on enter to prepare for sanitization
     */
    @Advice.OnMethodEnter
    public static void onStringMethodEnter(
            @Advice.This ResultSet resultSet,
            @Advice.Origin String methodName,
            @Advice.AllArguments Object[] args) {

        // Clear any previous sanitized value
        sanitizedReturnValue.remove();

        // 防止递归调用
        if (isProcessing.get()) {
            return;
        }

        try {
            isProcessing.set(true);
            System.err.println("[Agent-ResultSet] 🔍 OnEnter - Processing getString: method=" + methodName);

            // Only process getString methods
            if (methodName.contains("getString") && args != null && args.length > 0) {
                String columnName = extractColumnName(resultSet, args);
                System.err.println("[Agent-ResultSet] 🔍 OnEnter - Processing getString: column=" + columnName + " method=" + methodName);
            }
        } catch (Exception e) {
            System.err.println("[Agent-ResultSet] Error in onEnter: " + e.getMessage());
        } finally {
            isProcessing.set(false);
        }
    }

    /**
     * Intercept getString() methods to apply data sanitization
     * Using inline = false to ensure proper return value replacement
     */
    @Advice.OnMethodExit(onThrowable = Throwable.class, inline = false)
    @Advice.AssignReturned.ToReturned
    public static String onStringMethodExit(
            @Advice.This ResultSet resultSet,
            @Advice.Origin String methodName,
            @Advice.AllArguments Object[] args,
            @Advice.Return String returnValue,
            @Advice.Thrown Throwable throwable) {

        // 防止递归调用和异常情况
        if (isProcessing.get() || throwable != null || returnValue == null) {
            System.err.println("[Agent-ResultSet] 🚫 Skipping sanitization: processing=" + isProcessing.get() +
                             " throwable=" + (throwable != null) + " returnValue=" + (returnValue != null));
            return returnValue;
        }

        try {
            isProcessing.set(true);

            // Only process getString methods
            if (methodName.contains("getString") && args != null && args.length > 0) {
                String columnName = extractColumnName(resultSet, args);

                System.err.println("[Agent-ResultSet] 🔍 OnExit - Processing getString: column=" + columnName +
                                 " value='" + returnValue + "' method=" + methodName);

                if (columnName != null) {
                    DataCaptureConfig config = DataCaptureConfig.getInstance();

                    // Check if this column should be sanitized
                    if (config.isSanitizationEnabled() && config.isSensitiveColumn(columnName)) {
                        Object sanitizedValue = DataSanitizer.sanitizeValue(columnName, returnValue, config);
                        String sanitizedString = sanitizedValue.toString();

                        System.err.println("[Agent-ResultSet] 🛡️ CRITICAL - SANITIZATION APPLIED for '" + columnName +
                                         "': ORIGINAL='" + returnValue + "' -> SANITIZED='" + sanitizedString + "'");
                        System.err.println("[Agent-ResultSet] 🔄 CRITICAL - RETURNING SANITIZED VALUE: '" + sanitizedString + "'");
                        System.err.println("[Agent-ResultSet] 🎯 METHOD SIGNATURE: " + methodName);
                        System.err.flush();

                        // Verify the replacement will work
                        if (!sanitizedString.equals(returnValue)) {
                            System.err.println("[Agent-ResultSet] ✅ CONFIRMED - Return value will be REPLACED" + sanitizedString);
                            sanitizedReturnValue.set(sanitizedString);
                            return sanitizedString;  // This MUST replace the original return value
                        } else {
                            System.err.println("[Agent-ResultSet] ⚠️ SAME VALUES - No replacement needed");
                        }
                    } else {
                        System.err.println("[Agent-ResultSet] ℹ️ Column '" + columnName + "' does not require sanitization");
                    }
                } else {
                    System.err.println("[Agent-ResultSet] ⚠️ Could not extract column name for method=" + methodName +
                                     " with args=" + java.util.Arrays.toString(args));
                }
            } else {
                System.err.println("[Agent-ResultSet] 🔄 Method '" + methodName + "' does not match getString pattern or has no args");
            }
        } catch (Exception e) {
            System.err.println("[Agent-ResultSet] ❌ Error in sanitization: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isProcessing.set(false);
        }

        // Return original value if no sanitization needed
        System.err.println("[Agent-ResultSet] 🔄 RETURNING ORIGINAL VALUE: '" + returnValue + "'");
        return returnValue;
    }

    /**
     * Extract column name from ResultSet access, handling both index and name-based
     * access
     * Now integrates SQL column extraction for real field names
     */
    public static String extractColumnName(ResultSet resultSet, Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        try {
            Object firstArg = args[0];
            String rawColumnName = null;
            int columnIndex = -1;

            if (firstArg instanceof String) {
                // 按列名获取
                rawColumnName = (String) firstArg;
                System.err.println("[Agent-ResultSet] 获取列名: " + rawColumnName);
            } else if (firstArg instanceof Integer) {
                // 按列索引获取
                columnIndex = (Integer) firstArg;
                ResultSetMetaData metaData = resultSet.getMetaData();
                if (columnIndex > 0 && columnIndex <= metaData.getColumnCount()) {
                    rawColumnName = metaData.getColumnName(columnIndex);
                    System.err.println("[Agent-ResultSet] 按索引 " + columnIndex + " 获取列名: " + rawColumnName);
                }
            }

            if (rawColumnName != null) {
                // 尝试从 JdbcSpanStorage 获取 SQL 语句和真实列名
                SimpleSpan activeSpan = JdbcSpanStorage.getActiveJdbcSpan();
                String realColumnName = getRealColumnNameFromSpan(activeSpan, rawColumnName, columnIndex);

                if (realColumnName != null) {
                    System.err.println("[Agent-ResultSet] 使用真实列名: " + realColumnName + " (元数据: " + rawColumnName + ")");
                    return realColumnName;
                } else {
                    // 如果无法获取真实列名，使用标准化方法
                    String normalizedName = normalizeColumnName(rawColumnName);
                    System.err.println("[Agent-ResultSet] 使用标准化列名: " + normalizedName + " (元数据: " + rawColumnName + ")");
                    return normalizedName;
                }
            }
        } catch (Exception e) {
            System.err.println("[Agent-ResultSet] Error extracting column name: " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 从活跃的 JDBC Span 中获取真实的列名
     */
    private static String getRealColumnNameFromSpan(SimpleSpan activeSpan, String rawColumnName, int columnIndex) {
        if (activeSpan == null) {
            System.err.println("[Agent-ResultSet] No active JDBC span found");
            return null;
        }

        try {
            // 从 span 中获取 SQL 语句
            Object sqlAttr = activeSpan.getAttributes().get("db.statement");
            if (sqlAttr == null) {
                System.err.println("[Agent-ResultSet] No SQL statement found in span");
                return null;
            }

            String sqlStatement = sqlAttr.toString();
            List<String> realColumnNames = SqlColumnExtractor.extractRealColumnNames(sqlStatement);

            if (realColumnNames.isEmpty()) {
                System.err.println("[Agent-ResultSet] No real column names extracted from SQL");
                return null;
            }

            // 如果按索引访问，直接使用索引获取真实列名
            if (columnIndex > 0 && columnIndex <= realColumnNames.size()) {
                return realColumnNames.get(columnIndex - 1);
            }

            // 如果按名称访问，尝试匹配真实列名
            if (rawColumnName != null) {
                // 先检查是否直接匹配
                for (String realColumn : realColumnNames) {
                    if (realColumn.equalsIgnoreCase(rawColumnName)) {
                        return realColumn;
                    }
                }

                // 再检查模糊匹配（处理别名情况）
                String normalizedRaw = normalizeColumnName(rawColumnName);
                for (String realColumn : realColumnNames) {
                    if (realColumn.equalsIgnoreCase(normalizedRaw)) {
                        return realColumn;
                    }
                }

                System.err.println("[Agent-ResultSet] Could not match \"" + rawColumnName +
                                 "\" to any real column in: " + realColumnNames);
            }

        } catch (Exception e) {
            System.err.println("[Agent-ResultSet] Error getting real column name from span: " + e.getMessage());
        }

        return null;
    }

    /**
     * Normalize column names to handle Hibernate aliases and other variations
     * Examples:
     * - "email2_0_" -> "email"
     * - "password3_0_" -> "password"
     * - "user_email" -> "email" (if contains email)
     * - "user_password" -> "password" (if contains password)
     */
    private static String normalizeColumnName(String rawColumnName) {
        if (rawColumnName == null) {
            return null;
        }

        String normalized = rawColumnName.toLowerCase();

        // Handle Hibernate-style aliases: remove numeric suffixes like "2_0_"
        // Pattern: columnname + digit + "_" + digit + "_"
        if (normalized.matches(".*\\d+_\\d+_?$")) {
            // Find the first digit and remove everything from there
            for (int i = 0; i < normalized.length(); i++) {
                if (Character.isDigit(normalized.charAt(i))) {
                    normalized = normalized.substring(0, i);
                    break;
                }
            }
        }

        // Handle common column name patterns
        if (normalized.contains("email")) {
            return "email";
        } else if (normalized.contains("password") || normalized.contains("passwd") || normalized.contains("pwd")) {
            return "password";
        } else if (normalized.contains("phone") || normalized.contains("mobile") || normalized.contains("telephone")) {
            return "phone";
        }

        return normalized;
    }
}
