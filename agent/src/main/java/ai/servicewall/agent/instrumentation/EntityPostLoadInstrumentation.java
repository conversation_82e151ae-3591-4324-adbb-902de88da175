package ai.servicewall.agent.instrumentation;

import java.lang.reflect.Field;
import java.util.Collection;

import ai.servicewall.agent.config.DataCaptureConfig;
import net.bytebuddy.asm.Advice;

/**
 * JPA Entity Post-Load instrumentation for data sanitization
 * This provides a secondary layer of sanitization at the entity level
 * to ensure sensitive data is sanitized even if ResultSet-level sanitization is
 * bypassed
 */
public class EntityPostLoadInstrumentation {

    // 防止递归调用的ThreadLocal标记
    public static final ThreadLocal<Boolean> isProcessing = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    /**
     * Intercept JPA repository methods that return entities
     * This catches findAll(), findById(), etc.
     */
    @Advice.OnMethodExit
    public static void onRepositoryMethodExit(
            @Advice.Origin String methodName,
            @Advice.Return Object returnValue) {

        // 防止递归调用
        if (isProcessing.get() || returnValue == null) {
            return;
        }

        try {
            isProcessing.set(true);

            System.err.println("[Agent-EntityPostLoad] 🔍 Repository method called: " + methodName + " returnValue: "
                    + returnValue);
            System.err.flush();

            // Handle different return types
            if (returnValue instanceof Collection) {
                // Handle List<Entity>, Set<Entity>, etc.
                Collection<?> entities = (Collection<?>) returnValue;
                System.err
                        .println("[Agent-EntityPostLoad] 📋 Processing collection of " + entities.size() + " entities");
                for (Object entity : entities) {
                    sanitizeEntity(entity);
                }
            } else {
                System.err.println("[Agent-EntityPostLoad] 📋 Processing single entity: " + returnValue);
                // Handle single entity
                sanitizeEntity(returnValue);
            }

        } catch (Exception e) {
            System.err.println("[Agent-EntityPostLoad] Error in post-load sanitization: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isProcessing.set(false);
        }
    }

    /**
     * Sanitize sensitive fields in an entity object
     */
    public static void sanitizeEntity(Object entity) {
        if (entity == null) {
            return;
        }

        try {
            Class<?> entityClass = entity.getClass();
            String entityName = entityClass.getSimpleName();

            // Only process entities that look like domain objects
            if (!isLikelyEntity(entityClass)) {
                return;
            }

            System.err.println("[Agent-EntityPostLoad] 🛡️ Sanitizing entity: " + entityName);

            DataCaptureConfig config = DataCaptureConfig.getInstance();
            if (!config.isSanitizationEnabled()) {
                return;
            }

            // Get all fields including inherited ones
            Field[] fields = getAllFields(entityClass);

            for (Field field : fields) {
                String fieldName = field.getName();

                // Check if this field should be sanitized
                if (config.isSensitiveColumn(fieldName)) {
                    try {
                        field.setAccessible(true);
                        Object originalValue = field.get(entity);

                        if (originalValue instanceof String) {
                            String stringValue = (String) originalValue;
                            Object sanitizedValue = DataSanitizer.sanitizeValue(fieldName, stringValue, config);

                            if (!stringValue.equals(sanitizedValue)) {
                                field.set(entity, sanitizedValue);
                                System.err.println("[Agent-EntityPostLoad] ✅ Sanitized field '" + fieldName +
                                        "': " + stringValue + " -> " + sanitizedValue);
                                System.err.flush();
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("[Agent-EntityPostLoad] ⚠️ Could not sanitize field '" + fieldName + "': "
                                + e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("[Agent-EntityPostLoad] Error sanitizing entity: " + e.getMessage());
        }
    }

    /**
     * Check if a class looks like a JPA entity
     */
    private static boolean isLikelyEntity(Class<?> clazz) {
        String className = clazz.getName();

        // Skip proxy classes, collections, primitives, etc.
        if (className.contains("$") ||
                className.startsWith("java.") ||
                className.startsWith("javax.") ||
                className.startsWith("org.springframework.") ||
                className.startsWith("org.hibernate.") ||
                clazz.isPrimitive() ||
                clazz.isArray() ||
                Collection.class.isAssignableFrom(clazz)) {
            return false;
        }

        // Look for entity-like characteristics
        return className.contains(".model.") ||
                className.contains(".entity.") ||
                className.contains(".domain.") ||
                hasEntityAnnotations(clazz);
    }

    /**
     * Check if class has JPA entity annotations
     */
    private static boolean hasEntityAnnotations(Class<?> clazz) {
        try {
            // Check for common JPA annotations
            @SuppressWarnings("unchecked")
            Class<? extends java.lang.annotation.Annotation> entityClass = (Class<? extends java.lang.annotation.Annotation>) Class
                    .forName("javax.persistence.Entity");
            if (clazz.isAnnotationPresent(entityClass)) {
                return true;
            }

            @SuppressWarnings("unchecked")
            Class<? extends java.lang.annotation.Annotation> jakartaEntityClass = (Class<? extends java.lang.annotation.Annotation>) Class
                    .forName("jakarta.persistence.Entity");
            return clazz.isAnnotationPresent(jakartaEntityClass);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * Get all fields from class hierarchy
     */
    private static Field[] getAllFields(Class<?> clazz) {
        java.util.List<Field> fields = new java.util.ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && !currentClass.equals(Object.class)) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            for (Field field : declaredFields) {
                fields.add(field);
            }
            currentClass = currentClass.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }
}
