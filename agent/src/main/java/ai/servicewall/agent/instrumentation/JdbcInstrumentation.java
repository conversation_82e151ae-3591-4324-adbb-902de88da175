package ai.servicewall.agent.instrumentation;

import java.lang.reflect.Method;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.util.ReflectionUtils;

import ai.servicewall.agent.config.DataCaptureConfig;
import ai.servicewall.agent.context.TraceContext;
import ai.servicewall.agent.span.SimpleSpan;
import ai.servicewall.agent.util.SqlColumnExtractor;
import net.bytebuddy.asm.Advice;

/**
 * JDBC PreparedStatement 拦截器
 * 基于 ByteBuddy Advice 实现 OpenTelemetry Database 语义约定
 * 排除 executeWithFlags 方法以避免无用的追踪数据
 */
public class JdbcInstrumentation {

    // SQL 去重机制：防止同一个 SQL 查询产生多个 span
    private static final ConcurrentHashMap<String, AtomicLong> activeSqlOperations = new ConcurrentHashMap<>();

    /**
     * PreparedStatement.execute*() 方法进入时的拦截逻辑
     * 注意：executeWithFlags 方法已在 Agent.java 中被排除
     * 应用智能过滤，只追踪有价值的数据库操作
     */
    @Advice.OnMethodEnter
    public static JdbcSpanContext onEnter(
            @Advice.This PreparedStatement statement,
            @Advice.Origin Method method) {

        try {
            // 获取当前线程的 TraceContext
            TraceContext currentContext = TraceContext.current();
            if (currentContext == null) {
                return null; // 没有激活的追踪上下文
            }

            // 提取并过滤 SQL 语句
            String sqlStatement = extractSqlStatement(statement);
            if (!shouldTraceOperation(sqlStatement, method)) {
                return null; // 过滤掉不需要追踪的操作
            }

            // 检查是否已经有相同的 SQL 操作正在执行（去重）
            String sqlKey = generateSqlKey(sqlStatement, currentContext.getTraceId());
            if (!tryRegisterSqlOperation(sqlKey)) {
                return null; // 重复的 SQL 操作，过滤掉
            }

            // 创建子上下文
            TraceContext childContext = currentContext.createChild();

            // 创建 Database Client Span
            String operationName = extractOperationName(statement, method, sqlStatement);
            SimpleSpan span = new SimpleSpan(childContext, operationName, SimpleSpan.SpanKind.CLIENT);

            // Store the span in ThreadLocal for ResultSet instrumentation to access
            JdbcSpanStorage.setActiveJdbcSpan(span);
            System.out.println("[Agent-JDBC] Stored JDBC span in ThreadLocal for ResultSet access");

            // 设置数据库相关属性
            span.setAttribute("component", "jdbc")
                    .setAttribute("span.kind", "client")
                    .setAttribute("db.system", extractDbSystem(statement))
                    .setAttribute("db.operation", extractDbOperation(method))
                    .setAttribute("db.statement", sqlStatement);

            System.out.println("[Agent-JDBC] Created JDBC span: " + operationName + " for SQL: " + sqlStatement);

            return new JdbcSpanContext(span, TraceContext.makeCurrent(childContext), sqlKey);

        } catch (Exception e) {
            System.err.println("[Agent] Error in JDBC onEnter: " + e.getMessage());
            return null;
        }
    }

    protected static void freeFromImprisonment(ResultSet resultSet) {
        try {
            System.out.println(
                    "[Agent-JDBC] Starting freeFromImprisonment for ResultSet: " + resultSet.getClass().getName());

            // 修改可翻滚性 - 将 TYPE_FORWARD_ONLY 改为 TYPE_SCROLL_INSENSITIVE
            ReflectionUtils.doWithFields(resultSet.getClass(), field -> {
                try {
                    field.setAccessible(true);
                    Object originalValue = field.get(resultSet);
                    System.out.println("[Agent-JDBC] ResultSetType - 原始值: " + originalValue + " ("
                            + getResultSetTypeName((Integer) originalValue) + ")");

                    // 只有当前是 TYPE_FORWARD_ONLY 时才修改
                    if (originalValue != null && (Integer) originalValue == ResultSet.TYPE_FORWARD_ONLY) {
                        field.set(resultSet, ResultSet.TYPE_SCROLL_INSENSITIVE);
                        System.out.println("[Agent-JDBC] ResultSetType - 已修改为: " + ResultSet.TYPE_SCROLL_INSENSITIVE
                                + " (" + getResultSetTypeName(ResultSet.TYPE_SCROLL_INSENSITIVE) + ")");
                    } else {
                        System.out.println("[Agent-JDBC] ResultSetType - 无需修改，当前值: " + originalValue);
                    }
                } catch (Exception e) {
                    System.err.println("[Agent-JDBC] Error modifying resultSetType field: " + e.getMessage());
                }
            }, field -> "resultSetType".equals(field.getName()));

            // 修改并发性 - 将只读改为可更新
            ReflectionUtils.doWithFields(resultSet.getClass(), field -> {
                try {
                    field.setAccessible(true);
                    Object originalValue = field.get(resultSet);
                    System.out.println("[Agent-JDBC] ResultSetConcurrency - 原始值: " + originalValue + " ("
                            + getResultSetConcurrencyName((Integer) originalValue) + ")");

                    // 只有当前是 CONCUR_READ_ONLY 时才修改
                    if (originalValue != null && (Integer) originalValue == ResultSet.CONCUR_READ_ONLY) {
                        field.set(resultSet, ResultSet.CONCUR_UPDATABLE);
                        System.out.println("[Agent-JDBC] ResultSetConcurrency - 已修改为: " + ResultSet.CONCUR_UPDATABLE
                                + " (" + getResultSetConcurrencyName(ResultSet.CONCUR_UPDATABLE) + ")");
                    } else {
                        System.out.println("[Agent-JDBC] ResultSetConcurrency - 无需修改，当前值: " + originalValue);
                    }
                } catch (Exception e) {
                    System.err.println("[Agent-JDBC] Error modifying resultSetConcurrency field: " + e.getMessage());
                }
            }, field -> "resultSetConcurrency".equals(field.getName()));

            System.out.println("[Agent-JDBC] freeFromImprisonment completed successfully");

        } catch (Exception e) {
            System.err.println("[Agent-JDBC] Error in freeFromImprisonment: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * PreparedStatement.execute*() 方法退出时的拦截逻辑
     */
    @Advice.OnMethodExit(onThrowable = Throwable.class)
    public static void onExit(
            @Advice.Enter JdbcSpanContext spanContext,
            @Advice.Return Object result,
            @Advice.Thrown Throwable throwable) {

        if (spanContext == null) {
            return;
        }

        try {
            SimpleSpan span = spanContext.getSpan();

            // 设置执行结果
            if (throwable != null) {
                span.recordException(throwable)
                        .setStatus(SimpleSpan.SpanStatus.ERROR);
            } else {
                span.setStatus(SimpleSpan.SpanStatus.OK);

                // 设置结果相关属性
                if (result instanceof Integer) {
                    span.setAttribute("db.rows_affected", result);
                } else if (result instanceof Boolean) {
                    span.setAttribute("db.success", result);
                } else if (result instanceof ResultSet) {
                    // Handle SELECT queries - use direct reflection for data capture
                    ResultSet resultSet = (ResultSet) result;
                    span.setAttribute("db.result.type", "resultset");

                    System.out.println("[Agent-JDBC] ResultSet detected for span: " + span.getOperationName());

                    // 记录原始类型
                    int originalResultSetType = resultSet.getType();
                    System.out.println(
                            "[Agent-JDBC] Original ResultSet type: " + getResultSetTypeName(originalResultSetType));

                    // 调用 freeFromImprisonment 来修改 ResultSet 使其可滚动
                    freeFromImprisonment(resultSet);

                    // 验证修改后的类型
                    int modifiedResultSetType = resultSet.getType();
                    System.out.println(
                            "[Agent-JDBC] Modified ResultSet type: " + getResultSetTypeName(modifiedResultSetType));

                    // 调试：显示 resultSetType 字段的实际值
                    ReflectionUtils.doWithFields(resultSet.getClass(), field -> {
                        System.out.println("[Agent-JDBC] Field '" + field.getName() + "' type: " + field.getType()
                                + ", value: " + field.get(resultSet));
                    }, field -> "resultSetType".equals(field.getName()));

                    try {
                        // 尝试移动到第一行之前，为后续数据读取做准备
                        resultSet.beforeFirst();
                        System.out.println("[Agent-JDBC] Successfully positioned ResultSet before first row");
                    } catch (Exception e) {
                        System.err.println("[Agent-JDBC] Warning: Could not position ResultSet before first row: "
                                + e.getMessage());
                    }

                    try {
                        // 现在 ResultSet 应该是可滚动的，直接使用安全反射捕获
                        if (modifiedResultSetType != ResultSet.TYPE_FORWARD_ONLY) {
                            System.out.println(
                                    "[Agent-JDBC] ResultSet is now scrollable - using safe reflection capture");

                            // 直接使用安全反射捕获数据
                            captureResultSetDataBySafeReflection(resultSet, span);
                            span.setAttribute("db.result.capture_method", "safe_reflection_after_modification");
                            span.setAttribute("db.result.original_type", getResultSetTypeName(originalResultSetType));
                            span.setAttribute("db.result.modified_type", getResultSetTypeName(modifiedResultSetType));

                        } else {
                            System.out.println(
                                    "[Agent-JDBC] ResultSet modification failed, still TYPE_FORWARD_ONLY - delegating to ResultSetInstrumentation");

                            // 注册 ResultSet 用于生命周期跟踪
                            JdbcSpanStorage.registerResultSet(resultSet);

                            span.setAttribute("db.result.delegated_to_hook", true);
                            span.setAttribute("db.result.type_forward_only", true);
                            span.setAttribute("db.result.modification_failed", true);
                            // 设置基本元数据
                            try {
                                ResultSetMetaData metaData = resultSet.getMetaData();
                                int columnCount = metaData.getColumnCount();

                                // 尝试从 SQL 语句中提取真实列名
                                String sqlStatement = (String) span.getAttributes().get("db.statement");
                                List<String> realColumnNames = SqlColumnExtractor.extractRealColumnNames(sqlStatement);

                                List<String> columnNames = new ArrayList<>();
                                List<String> columnAliases = new ArrayList<>();
                                List<String> columnTypes = new ArrayList<>();

                                for (int i = 1; i <= columnCount; i++) {
                                    String metadataColumnName = metaData.getColumnName(i);
                                    String metadataColumnLabel = metaData.getColumnLabel(i);

                                    // 优先使用从 SQL 解析出的真实列名
                                    String realColumnName;
                                    if (i <= realColumnNames.size()) {
                                        realColumnName = realColumnNames.get(i - 1);
                                        System.out.println(
                                                "[Agent-JDBC] Using SQL-parsed real column: " + realColumnName +
                                                        " (metadata: " + metadataColumnName + ")");
                                    } else {
                                        realColumnName = metadataColumnName;
                                        System.out
                                                .println("[Agent-JDBC] Using metadata column name: " + realColumnName);
                                    }

                                    columnNames.add(realColumnName);
                                    columnAliases.add(metadataColumnLabel);
                                    columnTypes.add(metaData.getColumnTypeName(i));
                                }

                                span.setAttribute("db.result.column_count", columnCount);
                                span.setAttribute("db.result.column_names", columnNames); // 真实列名
                                span.setAttribute("db.result.column_aliases", columnAliases); // 别名或标签
                                span.setAttribute("db.result.column_types", columnTypes);
                                span.setAttribute("db.result.sql_parsed_columns", realColumnNames.size());
                            } catch (Exception me) {
                                System.err.println("[Agent-JDBC] Error capturing metadata: " + me.getMessage());
                            }
                        }

                    } catch (Exception e) {
                        System.err.println("[Agent-JDBC] Error processing ResultSet: " + e.getMessage());
                        span.setAttribute("db.result.capture_error", e.getMessage());
                        span.setAttribute("db.result.capture_method", "reflection_failed");
                    }

                    span.setStatus(SimpleSpan.SpanStatus.OK);
                }
            }

            // 结束 Span
            span.end();

        } catch (Exception e) {
            System.err.println("[Agent] Error in JDBC onExit: " + e.getMessage());
        } finally {
            // 清理上下文和 SQL 操作记录
            if (spanContext != null) {
                spanContext.getScope().close();
                unregisterSqlOperation(spanContext.getSqlKey());

                // 只有在没有活跃 ResultSet 时才清理 JdbcSpan
                if (!JdbcSpanStorage.hasActiveResultSets()) {
                    System.out.println("[Agent-JDBC] No active ResultSets, clearing JdbcSpan immediately");
                    JdbcSpanStorage.clearActiveJdbcSpan();
                } else {
                    System.out.println("[Agent-JDBC] Active ResultSets found, keeping JdbcSpan for sanitization");
                }
            }
        }
    }

    /**
     * 通过安全反射捕获可滚动 ResultSet 数据（不影响应用程序数据读取）
     */
    private static void captureResultSetDataBySafeReflection(ResultSet resultSet, SimpleSpan span) {
        DataCaptureConfig config = DataCaptureConfig.getInstance();
        if (!config.isCaptureEnabled()) {
            span.setAttribute("db.result.capture_method", "disabled");
            return;
        }

        try {
            // 获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            List<String> columnNames = new ArrayList<>();
            List<String> columnTypes = new ArrayList<>();
            for (int i = 1; i <= columnCount; i++) {
                columnNames.add(metaData.getColumnName(i));
                columnTypes.add(metaData.getColumnTypeName(i));
            }

            // 设置元数据属性
            span.setAttribute("db.result.column_count", columnCount);
            span.setAttribute("db.result.column_names", columnNames);
            span.setAttribute("db.result.column_types", columnTypes);
            span.setAttribute("db.result.capture_config_max_rows", config.getMaxRows());

            // 通过反射读取所有行数据
            List<Map<String, Object>> capturedRows = new ArrayList<>();
            int rowCount = 0;

            System.out.println("[Agent-JDBC] Starting reflection-based data capture for " + columnCount + " columns");

            // 保存当前位置
            int currentRow = resultSet.getRow();

            // 移动到开始位置
            resultSet.beforeFirst();

            while (resultSet.next() && capturedRows.size() < config.getMaxRows()) {
                rowCount++;
                Map<String, Object> rowData = new HashMap<>();

                for (int i = 1; i <= columnCount; i++) {
                    String columnName = columnNames.get(i - 1);
                    Object originalValue = resultSet.getObject(i);

                    // Apply sanitization to the traced value only
                    Object sanitizedValue = DataSanitizer.sanitizeValue(columnName, originalValue, config);
                    rowData.put(columnName, sanitizedValue);

                    System.out.println("[Agent-JDBC] Captured: " + columnName + " = " + sanitizedValue +
                            (originalValue != sanitizedValue ? " (sanitized)" : ""));
                }

                capturedRows.add(rowData);
                System.out.println(
                        "[Agent-JDBC] Successfully captured row " + rowCount + " with " + rowData.size() + " columns");
            }

            // 继续计数剩余行数（如果有）
            while (resultSet.next()) {
                rowCount++;
            }

            // 恢复到原始位置（如果可能）
            if (currentRow > 0) {
                resultSet.absolute(currentRow);
            } else {
                resultSet.beforeFirst();
            }

            // 设置最终结果
            span.setAttribute("db.result.row_count", rowCount);
            span.setAttribute("db.result.sample_size", capturedRows.size());

            if (!capturedRows.isEmpty()) {
                span.setAttribute("db.result.sample_rows", capturedRows);

                if (rowCount > capturedRows.size()) {
                    span.setAttribute("db.result.truncated", true);
                    span.setAttribute("db.result.truncated_at", config.getMaxRows());
                }
            }

            span.setAttribute("db.result.capture_method", "safe_reflection_success");

            System.out.println("[Agent-JDBC] Safe reflection capture completed: " + rowCount + " rows total, " +
                    capturedRows.size() + " rows captured");

        } catch (Exception e) {
            System.err.println("[Agent-JDBC] Error in safe reflection capture: " + e.getMessage());
            span.setAttribute("db.result.capture_error", e.getMessage());
            span.setAttribute("db.result.capture_method", "safe_reflection_failed");
        }
    }

    /**
     * Capture only metadata from ResultSet without consuming data
     * Safe method that doesn't interfere with normal ResultSet processing
     */
    public static void captureResultSetMetadataOnly(ResultSet resultSet, SimpleSpan span) {
        DataCaptureConfig config = DataCaptureConfig.getInstance();

        if (!config.isCaptureEnabled()) {
            return;
        }

        try {
            // Only capture metadata - don't consume the ResultSet
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 尝试从 SQL 语句中提取真实列名
            String sqlStatement = (String) span.getAttributes().get("db.statement");
            List<String> realColumnNames = SqlColumnExtractor.extractRealColumnNames(sqlStatement);

            List<String> columnNames = new ArrayList<>();
            List<String> columnAliases = new ArrayList<>();
            List<String> columnTypes = new ArrayList<>();

            for (int i = 1; i <= columnCount; i++) {
                String metadataColumnName = metaData.getColumnName(i);
                String metadataColumnLabel = metaData.getColumnLabel(i);

                // 优先使用从 SQL 解析出的真实列名
                String realColumnName;
                if (i <= realColumnNames.size()) {
                    realColumnName = realColumnNames.get(i - 1);
                    System.out.println("[Agent-JDBC-Metadata] Using SQL-parsed real column: " + realColumnName +
                            " (metadata: " + metadataColumnName + ")");
                } else {
                    realColumnName = metadataColumnName;
                    System.out.println("[Agent-JDBC-Metadata] Using metadata column name: " + realColumnName);
                }

                columnNames.add(realColumnName);
                columnAliases.add(metadataColumnLabel);
                columnTypes.add(metaData.getColumnTypeName(i));
            }

            // Set metadata attributes
            span.setAttribute("db.result.column_count", columnCount);
            span.setAttribute("db.result.column_names", columnNames); // 真实列名
            span.setAttribute("db.result.column_aliases", columnAliases); // 别名或标签
            span.setAttribute("db.result.column_types", columnTypes);
            span.setAttribute("db.result.capture_config_max_rows", config.getMaxRows());
            span.setAttribute("db.result.capture_method", "metadata_only");
            span.setAttribute("db.result.sql_parsed_columns", realColumnNames.size());

        } catch (Exception e) {
            System.err.println("[Agent] Error capturing ResultSet metadata: " + e.getMessage());
            span.setAttribute("db.result.capture_error", e.getMessage());
            span.setAttribute("db.result.capture_method", "metadata_failed");
        }
    }

    /**
     * Capture data from ResultSet for SELECT queries (immediate capture)
     * This method directly reads the ResultSet and captures data with sanitization
     */
    public static void captureResultSetDataImmediate(ResultSet resultSet, SimpleSpan span) {
        DataCaptureConfig config = DataCaptureConfig.getInstance();

        if (!config.isCaptureEnabled()) {
            return;
        }

        try {
            // Get SQL statement from span attributes to check if table should be excluded
            Object sqlAttr = span.getAttributes().get("db.statement");
            String sql = sqlAttr != null ? sqlAttr.toString() : null;

            if (DataSanitizer.shouldSkipDataCapture(sql, config)) {
                span.setAttribute("db.result.capture_method", "skipped");
                return;
            }

            // Capture metadata first
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            List<String> columnNames = new ArrayList<>();
            List<String> columnTypes = new ArrayList<>();

            for (int i = 1; i <= columnCount; i++) {
                columnNames.add(metaData.getColumnName(i));
                columnTypes.add(metaData.getColumnTypeName(i));
            }

            // Set metadata
            span.setAttribute("db.result.column_count", columnCount);
            span.setAttribute("db.result.column_names", columnNames);
            span.setAttribute("db.result.column_types", columnTypes);
            span.setAttribute("db.result.capture_config_max_rows", config.getMaxRows());

            // Capture actual row data
            List<Map<String, Object>> rows = new ArrayList<>();
            int rowCount = 0;

            // Reset cursor to beginning if possible
            try {
                if (resultSet.getType() != ResultSet.TYPE_FORWARD_ONLY) {
                    resultSet.beforeFirst();
                }
            } catch (Exception e) {
                // Ignore if cursor reset fails
            }

            // Read all rows up to the limit
            while (resultSet.next() && rows.size() < config.getMaxRows()) {
                rowCount++;
                Map<String, Object> rowData = new HashMap<>();

                for (int i = 1; i <= columnCount; i++) {
                    String columnName = columnNames.get(i - 1);
                    Object value = resultSet.getObject(i);

                    // Use original value without masking
                    rowData.put(columnName, value);
                }

                rows.add(rowData);
            }

            // Continue counting rows if there are more
            while (resultSet.next()) {
                rowCount++;
            }

            // Reset cursor to beginning for normal processing
            try {
                if (resultSet.getType() != ResultSet.TYPE_FORWARD_ONLY) {
                    resultSet.beforeFirst();
                    System.out.println("[Agent-JDBC] Reset ResultSet cursor to beginning for normal processing");
                } else {
                    System.out.println("[Agent-JDBC] Cannot reset FORWARD_ONLY ResultSet cursor");
                }
            } catch (Exception e) {
                System.err.println("[Agent-JDBC] Failed to reset ResultSet cursor: " + e.getMessage());
            }

            // Attach captured data to span
            span.setAttribute("db.result.row_count", rowCount);
            span.setAttribute("db.result.sample_size", rows.size());

            if (!rows.isEmpty()) {
                span.setAttribute("db.result.sample_rows", rows);

                if (rowCount > rows.size()) {
                    span.setAttribute("db.result.truncated", true);
                    span.setAttribute("db.result.truncated_at", config.getMaxRows());
                }
            }

            span.setAttribute("db.result.capture_method", "immediate_success");

        } catch (Exception e) {
            System.err.println("[Agent] Error capturing ResultSet data immediately: " + e.getMessage());
            span.setAttribute("db.result.capture_error", e.getMessage());
            span.setAttribute("db.result.capture_method", "immediate_failed");
        }
    }

    /**
     * 判断是否应该追踪该操作
     * 过滤掉低价值的数据库操作
     */
    public static boolean shouldTraceOperation(String sqlStatement, Method method) {
        // 1. 过滤掉无效的 SQL 语句
        if (sqlStatement == null || sqlStatement.trim().isEmpty() ||
                "SQL statement unavailable".equals(sqlStatement)) {
            return false;
        }

        // 2. 过滤掉 HikariProxy wrapping 信息
        if (sqlStatement.contains("@") && sqlStatement.contains("wrapping")) {
            return false;
        }

        // 3. 过滤掉系统内部查询
        String sqlUpper = sqlStatement.toUpperCase();
        if (sqlUpper.contains("INFORMATION_SCHEMA") ||
                sqlUpper.contains("PG_CATALOG") ||
                sqlUpper.contains("SYS.") ||
                sqlUpper.contains("DUAL")) {
            return false;
        }

        // 4. 过滤掉连接池内部操作（通常是短的、非业务查询）
        if (sqlUpper.startsWith("SELECT 1") ||
                sqlUpper.startsWith("SELECT COUNT") && sqlUpper.length() < 50) {
            return false;
        }

        // 5. 只保留有意义的业务操作
        return sqlUpper.startsWith("SELECT") ||
                sqlUpper.startsWith("INSERT") ||
                sqlUpper.startsWith("UPDATE") ||
                sqlUpper.startsWith("DELETE");
    }

    /**
     * 提取操作名称（使用预提取的 SQL 语句）
     */
    public static String extractOperationName(PreparedStatement statement, Method method, String sqlStatement) {
        if (sqlStatement != null && !sqlStatement.trim().isEmpty() &&
                !"SQL statement unavailable".equals(sqlStatement)) {
            // 从 SQL 语句中提取操作类型
            String sql = sqlStatement.trim().toUpperCase();
            if (sql.startsWith("SELECT")) {
                return "SELECT";
            } else if (sql.startsWith("INSERT")) {
                return "INSERT";
            } else if (sql.startsWith("UPDATE")) {
                return "UPDATE";
            } else if (sql.startsWith("DELETE")) {
                return "DELETE";
            }
        }

        // 回退到方法名
        return "JDBC." + method.getName();
    }

    public static String getResultSetTypeName(int type) {
        switch (type) {
            case ResultSet.TYPE_FORWARD_ONLY:
                return "TYPE_FORWARD_ONLY";
            case ResultSet.TYPE_SCROLL_INSENSITIVE:
                return "TYPE_SCROLL_INSENSITIVE";
            case ResultSet.TYPE_SCROLL_SENSITIVE:
                return "TYPE_SCROLL_SENSITIVE";
            default:
                return "UNKNOWN(" + type + ")";
        }
    }

    public static String getResultSetConcurrencyName(int concurrency) {
        switch (concurrency) {
            case ResultSet.CONCUR_READ_ONLY:
                return "CONCUR_READ_ONLY";
            case ResultSet.CONCUR_UPDATABLE:
                return "CONCUR_UPDATABLE";
            default:
                return "UNKNOWN(" + concurrency + ")";
        }
    }

    /**
     * 提取数据库系统类型
     */
    public static String extractDbSystem(PreparedStatement statement) {
        try {
            String url = statement.getConnection().getMetaData().getURL();
            if (url.contains("postgresql")) {
                return "postgresql";
            } else if (url.contains("mysql")) {
                return "mysql";
            } else if (url.contains("h2")) {
                return "h2";
            } else if (url.contains("oracle")) {
                return "oracle";
            } else if (url.contains("sqlserver")) {
                return "mssql";
            }
        } catch (Exception e) {
            // 忽略异常，返回默认值
        }
        return "unknown";
    }

    /**
     * 提取数据库操作类型
     */
    public static String extractDbOperation(Method method) {
        String methodName = method.getName().toLowerCase();

        if (methodName.contains("execute")) {
            return "execute";
        } else if (methodName.contains("query")) {
            return "query";
        } else if (methodName.contains("update")) {
            return "update";
        }

        return methodName;
    }

    /**
     * 提取 SQL 语句（改进的版本）
     */
    public static String extractSqlStatement(PreparedStatement statement) {
        try {
            // 尝试通过 toString() 获取 SQL - 大多数数据库驱动支持
            String toString = statement.toString();
            if (toString == null || toString.trim().isEmpty()) {
                return null;
            }

            // 处理 HikariProxy 格式: "HikariProxyPreparedStatement@xxx wrapping <SQL>"
            if (toString.contains("wrapping ")) {
                int wrappingIndex = toString.indexOf("wrapping ");
                if (wrappingIndex > 0 && wrappingIndex + 9 < toString.length()) {
                    String extractedSql = toString.substring(wrappingIndex + 9).trim();
                    // 只返回干净的 SQL，不包含对象地址
                    if (isValidSql(extractedSql)) {
                        return extractedSql;
                    }
                }
                // HikariProxy 但无法提取干净 SQL，返回 null 进行过滤
                return null;
            }

            // 处理常见的 PreparedStatement toString 格式
            if (toString.contains(": ")) {
                int colonIndex = toString.indexOf(": ");
                if (colonIndex > 0 && colonIndex + 2 < toString.length()) {
                    String extractedSql = toString.substring(colonIndex + 2).trim();
                    if (isValidSql(extractedSql)) {
                        return extractedSql;
                    }
                }
            }

            // 直接检查是否包含 SQL 关键字
            if (isValidSql(toString)) {
                return toString;
            }

        } catch (Exception e) {
            // 忽略异常
        }

        // 返回 null 而不是 "SQL statement unavailable"，让过滤器处理
        return null;
    }

    /**
     * 验证是否为有效的 SQL 语句
     */
    public static boolean isValidSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String upper = sql.trim().toUpperCase();
        return upper.startsWith("SELECT") ||
                upper.startsWith("INSERT") ||
                upper.startsWith("UPDATE") ||
                upper.startsWith("DELETE");
    }

    /**
     * 生成 SQL 操作的唯一键（仅在当前trace内去重）
     */
    public static String generateSqlKey(String sqlStatement, String traceId) {
        // 使用当前trace的threadId + SQL 的哈希值来生成唯一键
        // 这样只在同一线程的同一次请求中去重，不会跨请求影响
        String threadId = Thread.currentThread().getName();
        return threadId + "#" + traceId + "#" + Math.abs(sqlStatement.hashCode());
    }

    /**
     * 尝试注册 SQL 操作，如果已经存在则返回 false
     */
    public static boolean tryRegisterSqlOperation(String sqlKey) {
        AtomicLong counter = activeSqlOperations.get(sqlKey);
        if (counter == null) {
            // 第一次出现这个 SQL，允许追踪
            activeSqlOperations.put(sqlKey, new AtomicLong(1));
            return true;
        } else {
            // 已经有相同的 SQL 在执行，拒绝追踪
            return false;
        }
    }

    /**
     * 取消注册 SQL 操作
     */
    public static void unregisterSqlOperation(String sqlKey) {
        if (sqlKey != null) {
            activeSqlOperations.remove(sqlKey);
        }
    }

    /**
     * 用于在 onEnter 和 onExit 之间传递信息的上下文类
     */
    public static class JdbcSpanContext {
        private final SimpleSpan span;
        private final TraceContext.Scope scope;
        private final String sqlKey;

        public JdbcSpanContext(SimpleSpan span, TraceContext.Scope scope, String sqlKey) {
            this.span = span;
            this.scope = scope;
            this.sqlKey = sqlKey;
        }

        public SimpleSpan getSpan() {
            return span;
        }

        public TraceContext.Scope getScope() {
            return scope;
        }

        public String getSqlKey() {
            return sqlKey;
        }
    }
}
