package ai.servicewall.agent.instrumentation;

import java.lang.reflect.Method;

import javax.servlet.http.HttpServletRequest;

import ai.servicewall.agent.context.TraceContext;
import ai.servicewall.agent.span.SimpleSpan;
import net.bytebuddy.asm.Advice;

/**
 * Spring Controller HTTP 请求拦截器
 * 基于 ByteBuddy Advice 实现 OpenTelemetry HTTP 语义约定
 */
public class SpringControllerInstrumentation {

    /**
     * 方法进入时的拦截逻辑 - 创建实际的 SimpleSpan 并设置 trace context
     */
    @Advice.OnMethodEnter
    public static TraceContext.Scope onEnter(
        @Advice.Origin Method method,
        @Advice.AllArguments Object[] args) {

        try {
            // 创建简化的 JSON 输出来模拟 OpenTelemetry 格式（保持向后兼容）
            String operationName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
            String httpMethod = extractHttpMethod(method, args);
            String httpTarget = extractHttpTarget(method, args);

            System.out.println("[OTEL-TRACE] Starting HTTP Request: " + httpMethod + " " + httpTarget + " (" + operationName + ")");

            // 创建实际的 span 对象
            TraceContext context = TraceContext.createRoot();
            SimpleSpan span = new SimpleSpan(context, operationName, SimpleSpan.SpanKind.SERVER);

            // 设置 HTTP 属性
            span.setAttribute("http.method", httpMethod);
            span.setAttribute("http.target", httpTarget);
            span.setAttribute("http.scheme", "http");
            span.setAttribute("component", "spring-web");

            // 存储到线程本地变量（用于 onExit）
            ThreadLocalSpanStorage.setCurrentSpan(span);

            // 设置 TraceContext 为当前上下文，这样 JDBC 插桩可以获取到
            TraceContext.Scope scope = TraceContext.makeCurrent(context);
            return scope;

        } catch (Throwable e) {
            // 忽略所有异常
            return null;
        }
    }

    /**
     * 方法退出时的拦截逻辑 - 结束 SimpleSpan
     */
    @Advice.OnMethodExit(onThrowable = Throwable.class)
    public static void onExit(
        @Advice.Origin Method method,
        @Advice.AllArguments Object[] args,
        @Advice.Enter TraceContext.Scope scope,
        @Advice.Thrown Throwable throwable) {

        try {
            String operationName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
            String httpMethod = extractHttpMethod(method, args);
            String httpTarget = extractHttpTarget(method, args);

            // 获取当前 span
            SimpleSpan span = ThreadLocalSpanStorage.getCurrentSpan();
            if (span != null) {
                // 设置状态和响应属性
                if (throwable != null) {
                    span.setStatus(SimpleSpan.SpanStatus.ERROR);
                    span.recordException(throwable);
                    span.setAttribute("http.status_code", 500);
                    System.out.println("[OTEL-TRACE] HTTP Request completed with ERROR: " + httpMethod + " " + httpTarget + " (" + operationName + ") - " + throwable.getClass().getSimpleName());
                } else {
                    span.setStatus(SimpleSpan.SpanStatus.OK);
                    span.setAttribute("http.status_code", 200);
                    System.out.println("[OTEL-TRACE] HTTP Request completed successfully: " + httpMethod + " " + httpTarget + " (" + operationName + ")");
                }

                // 结束 span（这将触发导出到 batch processor）
                span.end();

                // 清理线程本地变量
                ThreadLocalSpanStorage.removeCurrentSpan();
            } else {
                // 回退到原来的日志输出
                if (throwable != null) {
                    System.out.println("[OTEL-TRACE] HTTP Request completed with ERROR: " + httpMethod + " " + httpTarget + " (" + operationName + ") - " + throwable.getClass().getSimpleName());
                } else {
                    System.out.println("[OTEL-TRACE] HTTP Request completed successfully: " + httpMethod + " " + httpTarget + " (" + operationName + ")");
                }
            }

        } catch (Throwable e) {
            // 确保清理线程本地变量
            ThreadLocalSpanStorage.removeCurrentSpan();
        } finally {
            // 清理 TraceContext scope
            if (scope != null) {
                scope.close();
            }
        }
    }

    /**
     * 安全地推断 HTTP 方法
     */
    public static String inferHttpMethod(String methodName) {
        String lowerName = methodName.toLowerCase();
        if (lowerName.contains("register") || lowerName.contains("login") || lowerName.startsWith("post")) {
            return "POST";
        } else if (lowerName.contains("get") || lowerName.contains("find") || lowerName.contains("list")) {
            return "GET";
        } else if (lowerName.contains("put") || lowerName.contains("update")) {
            return "PUT";
        } else if (lowerName.contains("delete") || lowerName.contains("remove")) {
            return "DELETE";
        }
        return "GET"; // 默认
    }

    /**
     * 安全地推断 HTTP 路径
     */
    public static String inferHttpTarget(Method method) {
        String className = method.getDeclaringClass().getSimpleName().toLowerCase();
        String methodName = method.getName().toLowerCase();

        // 移除 "Controller" 后缀
        if (className.endsWith("controller")) {
            className = className.substring(0, className.length() - 10);
        }

        return "/api/" + className + "/" + methodName;
    }

    /**
     * 提取操作名称
     */
    public static String extractOperationName(Method method) {
        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        return className + "." + methodName;
    }

    /**
     * 提取 HTTP 方法
     */
    public static String extractHttpMethod(Method method, Object[] args) {
        // 简化实现：从方法名推断 HTTP 方法
        String methodName = method.getName().toLowerCase();

        if (methodName.startsWith("get") || methodName.contains("find") || methodName.contains("list")) {
            return "GET";
        } else if (methodName.startsWith("post") || methodName.contains("create") || methodName.contains("add") || methodName.contains("register")) {
            return "POST";
        } else if (methodName.startsWith("put") || methodName.contains("update")) {
            return "PUT";
        } else if (methodName.startsWith("delete") || methodName.contains("remove")) {
            return "DELETE";
        }

        // 尝试从参数中查找 HttpServletRequest
        if (args != null) {
            for (Object arg : args) {
                if (arg != null && arg instanceof HttpServletRequest) {
                    return ((HttpServletRequest) arg).getMethod();
                }
            }
        }

        return "GET"; // 默认
    }

    /**
     * 提取 HTTP 路径
     */
    public static String extractHttpTarget(Method method, Object[] args) {
        // 尝试从参数中查找 HttpServletRequest
        if (args != null) {
            for (Object arg : args) {
                if (arg != null && arg instanceof HttpServletRequest) {
                    HttpServletRequest request = (HttpServletRequest) arg;
                    String requestURI = request.getRequestURI();
                    String queryString = request.getQueryString();
                    if (queryString != null) {
                        return requestURI + "?" + queryString;
                    }
                    return requestURI;
                }
            }
        }

        // 尝试从 Spring 注解中获取路径信息
        try {
            // 检查类级别的 @RequestMapping
            String basePath = "";
            java.lang.annotation.Annotation[] classAnnotations = method.getDeclaringClass().getAnnotations();
            for (java.lang.annotation.Annotation annotation : classAnnotations) {
                if (annotation.annotationType().getName().equals("org.springframework.web.bind.annotation.RequestMapping")) {
                    try {
                        java.lang.reflect.Method valueMethod = annotation.getClass().getMethod("value");
                        String[] values = (String[]) valueMethod.invoke(annotation);
                        if (values.length > 0) {
                            basePath = values[0];
                            break;
                        }
                    } catch (Exception e) {
                        // 忽略异常，继续尝试
                    }
                }
            }

            // 检查方法级别的映射注解
            String methodPath = "";
            java.lang.annotation.Annotation[] methodAnnotations = method.getAnnotations();
            for (java.lang.annotation.Annotation annotation : methodAnnotations) {
                String annotationName = annotation.annotationType().getName();
                if (annotationName.equals("org.springframework.web.bind.annotation.GetMapping") ||
                    annotationName.equals("org.springframework.web.bind.annotation.PostMapping") ||
                    annotationName.equals("org.springframework.web.bind.annotation.PutMapping") ||
                    annotationName.equals("org.springframework.web.bind.annotation.DeleteMapping") ||
                    annotationName.equals("org.springframework.web.bind.annotation.PatchMapping") ||
                    annotationName.equals("org.springframework.web.bind.annotation.RequestMapping")) {

                    try {
                        java.lang.reflect.Method valueMethod = annotation.getClass().getMethod("value");
                        String[] values = (String[]) valueMethod.invoke(annotation);
                        if (values.length > 0) {
                            methodPath = values[0];
                            break;
                        }
                    } catch (Exception e) {
                        // 忽略异常，继续尝试
                    }
                }
            }

            // 组合完整路径
            if (!basePath.isEmpty() || !methodPath.isEmpty()) {
                String fullPath = basePath + methodPath;
                if (!fullPath.startsWith("/")) {
                    fullPath = "/" + fullPath;
                }
                System.out.println("[DEBUG] Extracted path from annotations: basePath='" + basePath + "', methodPath='" + methodPath + "', fullPath='" + fullPath + "'");
                return fullPath;
            }
        } catch (Exception e) {
            System.out.println("[DEBUG] Annotation extraction failed: " + e.getMessage());
            // 如果注解解析失败，回退到推断逻辑
        }

        // 回退：从类和方法名推断路径
        String className = method.getDeclaringClass().getSimpleName().toLowerCase();
        String methodName = method.getName().toLowerCase();

        // 移除 "Controller" 后缀
        if (className.endsWith("controller")) {
            className = className.substring(0, className.length() - 10);
        }

        String fallbackPath = "/api/" + className + "/" + methodName;
        System.out.println("[DEBUG] Using fallback path: " + fallbackPath + " (class=" + className + ", method=" + methodName + ")");
        return fallbackPath;
    }

    /**
     * 线程本地存储，用于在 onEnter 和 onExit 之间传递 Span
     */
    public static class ThreadLocalSpanStorage {
        private static final ThreadLocal<SimpleSpan> CURRENT_SPAN = new ThreadLocal<>();

        public static void setCurrentSpan(SimpleSpan span) {
            CURRENT_SPAN.set(span);
        }

        public static SimpleSpan getCurrentSpan() {
            return CURRENT_SPAN.get();
        }

        public static void removeCurrentSpan() {
            CURRENT_SPAN.remove();
        }
    }
}
