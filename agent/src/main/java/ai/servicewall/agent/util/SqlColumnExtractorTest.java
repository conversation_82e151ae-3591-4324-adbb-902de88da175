package ai.servicewall.agent.util;

import java.util.List;

/**
 * SQL 列名解析器测试
 */
public class SqlColumnExtractorTest {
    
    public static void main(String[] args) {
        testSqlColumnExtractor();
    }
    
    public static void testSqlColumnExtractor() {
        System.out.println("=== SQL Column Extractor Test ===\n");
        
        // 测试用例
        String[] testSqls = {
            // 简单查询
            "SELECT name, email FROM users",
            "SELECT id, name, email FROM users WHERE id = 1",
            
            // 别名查询
            "SELECT name AS user_name, email AS user_email FROM users",
            "SELECT u.name AS user_name, u.email FROM users u",
            
            // 表前缀
            "SELECT u.name, u.email, p.title FROM users u JOIN posts p ON u.id = p.user_id",
            
            // 函数调用
            "SELECT COUNT(id), name FROM users GROUP BY name",
            "SELECT MAX(created_at) AS latest, name FROM users GROUP BY name",
            
            // SELECT *
            "SELECT * FROM users",
            
            // 复杂查询
            "SELECT DISTINCT u.name, COUNT(p.id) AS post_count FROM users u LEFT JOIN posts p ON u.id = p.user_id GROUP BY u.name",
            
            // 带引号的字段
            "SELECT `name`, 'email' FROM users",
            
            // 无效 SQL
            "UPDATE users SET name = 'test'",
            ""
        };
        
        for (int i = 0; i < testSqls.length; i++) {
            String sql = testSqls[i];
            System.out.println("Test " + (i + 1) + ":");
            System.out.println("SQL: " + sql);
            
            List<String> realColumns = SqlColumnExtractor.extractRealColumnNames(sql);
            System.out.println("Real columns: " + realColumns);
            
            List<SqlColumnExtractor.ColumnMapping> mappings = SqlColumnExtractor.extractColumnMappings(sql);
            System.out.println("Column mappings:");
            for (SqlColumnExtractor.ColumnMapping mapping : mappings) {
                System.out.println("  " + mapping);
            }
            
            System.out.println();
        }
        
        System.out.println("=== Test Completed ===");
    }
}