package ai.servicewall.agent.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL 列名提取器
 * 从 SELECT 语句中提取真实的列名，忽略别名
 * 主要用于数据脱敏场景，需要基于真实字段名进行脱敏规则匹配
 */
public class SqlColumnExtractor {
    
    // 匹配 SELECT 子句的正则表达式
    private static final Pattern SELECT_PATTERN = Pattern.compile(
        "\\bSELECT\\s+(.*?)\\s+FROM\\b", 
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );
    
    // 匹配函数调用的正则表达式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile(
        "\\b\\w+\\s*\\([^)]*\\)", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 匹配 AS 别名的正则表达式
    private static final Pattern AS_ALIAS_PATTERN = Pattern.compile(
        "(.+?)\\s+AS\\s+\\w+", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 从 SQL 语句中提取真实的列名
     * 
     * @param sqlStatement SQL 语句
     * @return 真实列名列表，如果无法解析则返回空列表
     */
    public static List<String> extractRealColumnNames(String sqlStatement) {
        List<String> realColumnNames = new ArrayList<>();
        
        if (sqlStatement == null || sqlStatement.trim().isEmpty()) {
            return realColumnNames;
        }
        
        try {
            // 标准化 SQL 语句
            String cleanSql = normalizeSQL(sqlStatement);
            
            // 匹配 SELECT 子句
            Matcher selectMatcher = SELECT_PATTERN.matcher(cleanSql);
            if (!selectMatcher.find()) {
                System.out.println("[SqlColumnExtractor] No SELECT clause found in: " + sqlStatement);
                return realColumnNames;
            }
            
            String selectClause = selectMatcher.group(1).trim();
            System.out.println("[SqlColumnExtractor] Found SELECT clause: " + selectClause);
            
            // 处理 SELECT * 的情况
            if ("*".equals(selectClause.trim())) {
                System.out.println("[SqlColumnExtractor] SELECT * detected - cannot extract specific column names");
                return realColumnNames; // 无法从 * 中提取具体列名
            }
            
            // 分割列名（处理逗号分隔）
            String[] columns = splitColumns(selectClause);
            
            for (String column : columns) {
                String realColumnName = extractRealColumnName(column.trim());
                if (realColumnName != null && !realColumnName.isEmpty()) {
                    realColumnNames.add(realColumnName);
                    System.out.println("[SqlColumnExtractor] Extracted real column: " + realColumnName + " from: " + column.trim());
                }
            }
            
        } catch (Exception e) {
            System.err.println("[SqlColumnExtractor] Error parsing SQL: " + e.getMessage());
        }
        
        return realColumnNames;
    }
    
    /**
     * 标准化 SQL 语句：移除多余空格、换行等
     */
    private static String normalizeSQL(String sql) {
        return sql.replaceAll("\\s+", " ").trim();
    }
    
    /**
     * 智能分割列名，考虑函数中的逗号
     */
    private static String[] splitColumns(String selectClause) {
        List<String> columns = new ArrayList<>();
        StringBuilder currentColumn = new StringBuilder();
        int parenthesesDepth = 0;
        
        for (char c : selectClause.toCharArray()) {
            if (c == '(') {
                parenthesesDepth++;
                currentColumn.append(c);
            } else if (c == ')') {
                parenthesesDepth--;
                currentColumn.append(c);
            } else if (c == ',' && parenthesesDepth == 0) {
                // 只有在括号外的逗号才作为分隔符
                columns.add(currentColumn.toString().trim());
                currentColumn = new StringBuilder();
            } else {
                currentColumn.append(c);
            }
        }
        
        // 添加最后一个列
        if (currentColumn.length() > 0) {
            columns.add(currentColumn.toString().trim());
        }
        
        return columns.toArray(new String[0]);
    }
    
    /**
     * 从单个列表达式中提取真实的列名
     */
    private static String extractRealColumnName(String columnExpression) {
        if (columnExpression == null || columnExpression.trim().isEmpty()) {
            return null;
        }
        
        String expression = columnExpression.trim();
        
        // 1. 处理 AS 别名：SELECT name AS user_name -> name
        Matcher asMatcher = AS_ALIAS_PATTERN.matcher(expression);
        if (asMatcher.find()) {
            expression = asMatcher.group(1).trim();
            System.out.println("[SqlColumnExtractor] Removed AS alias: " + expression);
        }
        
        // 2. 检查是否是函数调用：COUNT(id), SUM(amount) 等
        if (FUNCTION_PATTERN.matcher(expression).matches()) {
            System.out.println("[SqlColumnExtractor] Function detected, skipping: " + expression);
            return null; // 函数调用不是真实的列名
        }
        
        // 3. 处理表前缀：u.name -> name
        if (expression.contains(".")) {
            String[] parts = expression.split("\\.");
            if (parts.length == 2) {
                expression = parts[1].trim();
                System.out.println("[SqlColumnExtractor] Removed table prefix: " + expression);
            }
        }
        
        // 4. 移除引号
        expression = expression.replaceAll("['\"`]", "");
        
        // 5. 验证是否是有效的标识符
        if (isValidColumnIdentifier(expression)) {
            return expression;
        }
        
        System.out.println("[SqlColumnExtractor] Invalid column identifier: " + expression);
        return null;
    }
    
    /**
     * 验证是否是有效的列标识符
     */
    private static boolean isValidColumnIdentifier(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return false;
        }
        
        // 简单验证：字母开头，包含字母数字下划线
        return identifier.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
    
    /**
     * 创建列映射：别名 -> 真实列名
     */
    public static class ColumnMapping {
        private final String realColumnName;
        private final String aliasName;
        private final String fullExpression;
        
        public ColumnMapping(String realColumnName, String aliasName, String fullExpression) {
            this.realColumnName = realColumnName;
            this.aliasName = aliasName;
            this.fullExpression = fullExpression;
        }
        
        public String getRealColumnName() { return realColumnName; }
        public String getAliasName() { return aliasName; }
        public String getFullExpression() { return fullExpression; }
        
        @Override
        public String toString() {
            return String.format("ColumnMapping{real='%s', alias='%s', expression='%s'}", 
                               realColumnName, aliasName, fullExpression);
        }
    }
    
    /**
     * 提取完整的列映射信息
     */
    public static List<ColumnMapping> extractColumnMappings(String sqlStatement) {
        List<ColumnMapping> mappings = new ArrayList<>();
        
        if (sqlStatement == null || sqlStatement.trim().isEmpty()) {
            return mappings;
        }
        
        try {
            String cleanSql = normalizeSQL(sqlStatement);
            Matcher selectMatcher = SELECT_PATTERN.matcher(cleanSql);
            
            if (!selectMatcher.find()) {
                return mappings;
            }
            
            String selectClause = selectMatcher.group(1).trim();
            if ("*".equals(selectClause.trim())) {
                return mappings;
            }
            
            String[] columns = splitColumns(selectClause);
            
            for (String column : columns) {
                String trimmedColumn = column.trim();
                String realColumnName = extractRealColumnName(trimmedColumn);
                String aliasName = extractAliasName(trimmedColumn);
                
                if (realColumnName != null) {
                    mappings.add(new ColumnMapping(realColumnName, aliasName, trimmedColumn));
                }
            }
            
        } catch (Exception e) {
            System.err.println("[SqlColumnExtractor] Error extracting column mappings: " + e.getMessage());
        }
        
        return mappings;
    }
    
    /**
     * 提取别名
     */
    private static String extractAliasName(String columnExpression) {
        Matcher asMatcher = AS_ALIAS_PATTERN.matcher(columnExpression);
        if (asMatcher.find()) {
            // AS 别名：返回 AS 后面的部分
            String[] parts = columnExpression.split("\\s+AS\\s+", 2);
            if (parts.length == 2) {
                return parts[1].trim().replaceAll("['\"`]", "");
            }
        }
        
        // 没有显式别名，使用真实列名
        return extractRealColumnName(columnExpression);
    }
}