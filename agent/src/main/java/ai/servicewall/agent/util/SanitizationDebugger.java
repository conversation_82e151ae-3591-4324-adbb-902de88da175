package ai.servicewall.agent.util;

import ai.servicewall.agent.config.DataCaptureConfig;
import ai.servicewall.agent.span.SimpleSpan;
import ai.servicewall.agent.instrumentation.JdbcSpanStorage;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;

/**
 * 脱敏调试工具
 * 帮助诊断 ResultSetInstrumentation 脱敏问题
 */
public class SanitizationDebugger {
    
    /**
     * 诊断脱敏配置和状态
     */
    public static void debugSanitization(ResultSet resultSet, Object[] args, String returnValue) {
        System.err.println("========== 脱敏调试信息 ==========");
        
        try {
            // 1. 配置检查
            DataCaptureConfig config = DataCaptureConfig.getInstance();
            System.err.println("[DEBUG] 脱敏启用状态: " + config.isSanitizationEnabled());
            System.err.println("[DEBUG] 掩码字符串: '" + config.getMaskString() + "'");
            
            // 2. 参数分析
            if (args != null && args.length > 0) {
                Object firstArg = args[0];
                System.err.println("[DEBUG] 访问参数类型: " + firstArg.getClass().getSimpleName());
                System.err.println("[DEBUG] 访问参数值: " + firstArg);
            }
            
            // 3. ResultSetMetaData 分析
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            System.err.println("[DEBUG] 总列数: " + columnCount);
            
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                String columnLabel = metaData.getColumnLabel(i);
                String tableName = metaData.getTableName(i);
                
                System.err.println("[DEBUG] 列 " + i + ":");
                System.err.println("[DEBUG]   columnName: '" + columnName + "'");
                System.err.println("[DEBUG]   columnLabel: '" + columnLabel + "'");
                System.err.println("[DEBUG]   tableName: '" + tableName + "'");
                System.err.println("[DEBUG]   是否敏感: " + config.isSensitiveColumn(columnName));
            }
            
            // 4. JDBC Span 分析
            SimpleSpan activeSpan = JdbcSpanStorage.getActiveJdbcSpan();
            if (activeSpan != null) {
                System.err.println("[DEBUG] 找到活跃的 JDBC Span");
                Object sqlAttr = activeSpan.getAttributes().get("db.statement");
                if (sqlAttr != null) {
                    String sql = sqlAttr.toString();
                    System.err.println("[DEBUG] SQL 语句: " + sql);
                    
                    List<String> realColumns = SqlColumnExtractor.extractRealColumnNames(sql);
                    System.err.println("[DEBUG] 解析的真实列名: " + realColumns);
                } else {
                    System.err.println("[DEBUG] Span 中没有 SQL 语句");
                }
            } else {
                System.err.println("[DEBUG] 没有找到活跃的 JDBC Span");
            }
            
            // 5. 返回值分析
            System.err.println("[DEBUG] 返回值: '" + returnValue + "'");
            System.err.println("[DEBUG] 返回值类型: " + (returnValue != null ? returnValue.getClass().getSimpleName() : "null"));
            
        } catch (Exception e) {
            System.err.println("[DEBUG] 调试过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.err.println("================================");
    }
    
    /**
     * 测试敏感字段匹配
     */
    public static void testSensitiveColumnMatching() {
        System.err.println("========== 敏感字段匹配测试 ==========");
        
        DataCaptureConfig config = DataCaptureConfig.getInstance();
        String[] testColumns = {
            "email", "user_email", "email_address",
            "password", "passwd", "user_password",  
            "phone", "mobile", "telephone",
            "id", "name", "created_at"
        };
        
        for (String col : testColumns) {
            boolean isSensitive = config.isSensitiveColumn(col);
            System.err.println("[DEBUG] '" + col + "' -> 敏感: " + isSensitive);
            if (isSensitive) {
                DataCaptureConfig.SanitizationRule rule = config.getSanitizationRule(col);
                System.err.println("[DEBUG]   规则类型: " + (rule != null ? rule.getType() : "null"));
            }
        }
        
        System.err.println("===================================");
    }
}