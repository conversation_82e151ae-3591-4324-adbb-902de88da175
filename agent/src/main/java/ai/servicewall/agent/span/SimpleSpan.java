package ai.servicewall.agent.span;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import ai.servicewall.agent.Agent;
import ai.servicewall.agent.context.TraceContext;
import ai.servicewall.agent.exporter.ConsoleSpanExporter;
import ai.servicewall.agent.processor.SpanProcessor;

/**
 * Enhanced OpenTelemetry Span implementation
 * Supports proper timing, OTLP export, and OpenTelemetry standards
 */
public class SimpleSpan {
    private final String traceId;
    private final String spanId;
    private final String parentSpanId;
    private final String operationName;
    private final long startTimeUnixNanos;
    private final long startTimeNanos;
    private volatile long endTimeUnixNanos;
    private volatile long endTimeNanos;
    private final Map<String, Object> attributes;
    private volatile SpanKind kind;
    private volatile SpanStatus status;
    private volatile boolean ended;

    public SimpleSpan(TraceContext context, String operationName, SpanKind kind) {
        this.traceId = context.getTraceId();
        this.spanId = context.getSpanId();
        this.parentSpanId = context.getParentSpanId();
        this.operationName = operationName;

        // Capture both system nanoTime (for duration) and wall clock time (for timestamps)
        this.startTimeNanos = System.nanoTime();
        this.startTimeUnixNanos = System.currentTimeMillis() * 1_000_000; // Convert to nanoseconds

        this.endTimeUnixNanos = 0;
        this.endTimeNanos = 0;
        this.attributes = new HashMap<>();
        this.kind = kind != null ? kind : SpanKind.INTERNAL;
        this.status = SpanStatus.UNSET;
        this.ended = false;
    }

    /**
     * 设置 Span 属性
     */
    public SimpleSpan setAttribute(String key, Object value) {
        if (!ended && key != null && value != null) {
            attributes.put(key, value);
        }
        return this;
    }

    /**
     * 批量设置属性
     */
    public SimpleSpan setAttributes(Map<String, Object> attrs) {
        if (!ended && attrs != null) {
            attributes.putAll(attrs);
        }
        return this;
    }

    /**
     * 设置 Span 状态
     */
    public SimpleSpan setStatus(SpanStatus status) {
        if (!ended) {
            this.status = status != null ? status : SpanStatus.UNSET;
        }
        return this;
    }

    /**
     * 设置 Span 种类
     */
    public SimpleSpan setKind(SpanKind kind) {
        if (!ended) {
            this.kind = kind != null ? kind : SpanKind.INTERNAL;
        }
        return this;
    }

    /**
     * 记录异常
     */
    public SimpleSpan recordException(Throwable throwable) {
        if (!ended && throwable != null) {
            setAttribute("exception.type", throwable.getClass().getName());
            setAttribute("exception.message", throwable.getMessage());
            setStatus(SpanStatus.ERROR);
        }
        return this;
    }

    /**
     * 结束 Span 并导出
     */
    public void end() {
        if (!ended) {
            this.endTimeNanos = System.nanoTime();
            this.endTimeUnixNanos = System.currentTimeMillis() * 1_000_000;
            this.ended = true;

            // 导出到控制台（保持向后兼容）
            ConsoleSpanExporter.exportSimple(this);

            // 通过SpanProcessor处理span
            SpanProcessor processor = Agent.getSpanProcessor();
            if (processor != null) {
                try {
                    processor.onEnd(this);
                } catch (Exception e) {
                    System.err.printf("[SPAN-ERROR] Failed to process span: %s%n", e.getMessage());
                }
            }
        }
    }



    // Getters
    public String getTraceId() { return traceId; }
    public String getSpanId() { return spanId; }
    public String getParentSpanId() { return parentSpanId; }
    public String getOperationName() { return operationName; }
    public long getStartTimeNanos() { return startTimeNanos; }
    public long getEndTimeNanos() { return endTimeNanos; }
    public Map<String, Object> getAttributes() { return new HashMap<>(attributes); }
    public SpanKind getKind() { return kind; }
    public SpanStatus getStatus() { return status; }
    public boolean isEnded() { return ended; }

    /**
     * 获取持续时间 (毫秒)
     */
    public long getDurationMs() {
        long endTime = ended ? endTimeNanos : System.nanoTime();
        return TimeUnit.NANOSECONDS.toMillis(endTime - startTimeNanos);
    }

    /**
     * 获取开始时间戳 (Unix nano) - OpenTelemetry 标准
     */
    public long getStartTimeUnixNano() {
        return startTimeUnixNanos;
    }

    /**
     * 获取结束时间戳 (Unix nano) - OpenTelemetry 标准
     */
    public long getEndTimeUnixNano() {
        return ended ? endTimeUnixNanos : 0;
    }

    @Override
    public String toString() {
        return String.format("Span{traceId='%s', spanId='%s', name='%s', duration=%dms}",
            traceId, spanId, operationName, getDurationMs());
    }

    /**
     * Span 种类枚举 (参考 OpenTelemetry)
     */
    public enum SpanKind {
        INTERNAL,    // 内部操作
        SERVER,      // 服务端接收请求
        CLIENT,      // 客户端发起请求
        PRODUCER,    // 消息生产者
        CONSUMER     // 消息消费者
    }

    /**
     * Span 状态枚举 (参考 OpenTelemetry)
     */
    public enum SpanStatus {
        UNSET,    // 未设置
        OK,       // 成功
        ERROR     // 错误
    }
}
