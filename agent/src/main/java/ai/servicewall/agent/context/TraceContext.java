package ai.servicewall.agent.context;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * OpenTelemetry 兼容的追踪上下文实现
 * 使用 ThreadLocal 在同一线程内传递追踪信息
 */
public class TraceContext {
    private final String traceId;
    private final String spanId;
    private final String parentSpanId;
    private final Map<String, Object> baggage;

    private TraceContext(String traceId, String spanId, String parentSpanId, Map<String, Object> baggage) {
        this.traceId = traceId;
        this.spanId = spanId;
        this.parentSpanId = parentSpanId;
        this.baggage = baggage != null ? new HashMap<>(baggage) : new HashMap<>();
    }

    public String getTraceId() {
        return traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public String getParentSpanId() {
        return parentSpanId;
    }

    public Map<String, Object> getBaggage() {
        return new HashMap<>(baggage);
    }

    // 创建根上下文
    public static TraceContext createRoot() {
        return new TraceContext(
            generateTraceId(),
            generateSpanId(),
            null,
            new HashMap<>()
        );
    }

    // 创建子上下文
    public TraceContext createChild() {
        return new TraceContext(
            this.traceId,
            generateSpanId(),
            this.spanId,
            this.baggage
        );
    }

    // 生成 32 位十六进制 trace ID (128 bit)
    private static String generateTraceId() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        long high = random.nextLong();
        long low = random.nextLong();
        return String.format("%016x%016x", high, low);
    }

    // 生成 16 位十六进制 span ID (64 bit)
    private static String generateSpanId() {
        return String.format("%016x", ThreadLocalRandom.current().nextLong());
    }

    // ThreadLocal 存储当前上下文
    private static final ThreadLocal<TraceContext> CURRENT_CONTEXT = new ThreadLocal<>();

    /**
     * 获取当前线程的追踪上下文
     */
    public static TraceContext current() {
        return CURRENT_CONTEXT.get();
    }

    /**
     * 设置当前线程的追踪上下文
     * 返回一个 Scope 对象，用于自动清理
     */
    public static Scope makeCurrent(TraceContext context) {
        TraceContext previous = CURRENT_CONTEXT.get();
        CURRENT_CONTEXT.set(context);
        return new Scope(previous);
    }

    /**
     * 清理当前线程的上下文
     */
    public static void clear() {
        CURRENT_CONTEXT.remove();
    }

    /**
     * 自动清理的 Scope 实现
     */
    public static class Scope implements AutoCloseable {
        private final TraceContext previous;

        private Scope(TraceContext previous) {
            this.previous = previous;
        }

        @Override
        public void close() {
            if (previous != null) {
                CURRENT_CONTEXT.set(previous);
            } else {
                CURRENT_CONTEXT.remove();
            }
        }
    }

    @Override
    public String toString() {
        return String.format("TraceContext{traceId='%s', spanId='%s', parentSpanId='%s'}", 
            traceId, spanId, parentSpanId);
    }
}