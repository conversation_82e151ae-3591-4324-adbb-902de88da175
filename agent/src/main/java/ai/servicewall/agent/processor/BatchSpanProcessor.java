package ai.servicewall.agent.processor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import ai.servicewall.agent.exporter.OtlpSpanExporter;
import ai.servicewall.agent.span.SimpleSpan;

/**
 * BatchSpanProcessor implementation following OpenTelemetry patterns
 * Collects spans and exports them in batches for improved performance
 */
public class BatchSpanProcessor implements SpanProcessor {

    private static final int DEFAULT_MAX_EXPORT_BATCH_SIZE = 512;
    private static final int DEFAULT_EXPORT_TIMEOUT_MILLIS = 30000;
    private static final int DEFAULT_SCHEDULE_DELAY_MILLIS = 5000;
    private static final int DEFAULT_MAX_QUEUE_SIZE = 2048;

    private final OtlpSpanExporter exporter;
    private final int maxExportBatchSize;
    private final int exportTimeoutMillis;
    private final int scheduleDelayMillis;
    private final int maxQueueSize;

    private final BlockingQueue<SimpleSpan> spanQueue;
    private final ScheduledExecutorService scheduler;
    private final ExecutorService exportExecutor;
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);

    public BatchSpanProcessor(OtlpSpanExporter exporter) {
        this(exporter, DEFAULT_MAX_EXPORT_BATCH_SIZE, DEFAULT_EXPORT_TIMEOUT_MILLIS,
             DEFAULT_SCHEDULE_DELAY_MILLIS, DEFAULT_MAX_QUEUE_SIZE);
    }

    public BatchSpanProcessor(OtlpSpanExporter exporter, int maxExportBatchSize,
                            int exportTimeoutMillis, int scheduleDelayMillis, int maxQueueSize) {
        this.exporter = exporter;
        this.maxExportBatchSize = maxExportBatchSize;
        this.exportTimeoutMillis = exportTimeoutMillis;
        this.scheduleDelayMillis = scheduleDelayMillis;
        this.maxQueueSize = maxQueueSize;

        this.spanQueue = new ArrayBlockingQueue<>(maxQueueSize);
        this.scheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "otel-batch-span-processor");
            t.setDaemon(true);
            return t;
        });
        this.exportExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "otel-span-exporter");
            t.setDaemon(true);
            return t;
        });

        // Schedule periodic batch exports
        scheduler.scheduleWithFixedDelay(this::exportBatch, scheduleDelayMillis,
                                       scheduleDelayMillis, TimeUnit.MILLISECONDS);

        System.out.printf("[BATCH-PROCESSOR] Initialized with batchSize=%d, delay=%dms, queueSize=%d%n",
                         maxExportBatchSize, scheduleDelayMillis, maxQueueSize);
    }

    @Override
    public void onStart(SimpleSpan span) {
        // No action needed on start for batch processor
    }

    @Override
    public void onEnd(SimpleSpan span) {
        if (isShutdown.get()) {
            return;
        }

        System.out.printf("[BATCH-PROCESSOR] Received span: %s (queue size: %d)%n",
                         span.getOperationName(), spanQueue.size());

        if (!spanQueue.offer(span)) {
            // Queue is full, try to trigger immediate export
            System.err.println("[BATCH-PROCESSOR] Span queue full, dropping span: " + span.getOperationName());
            exportBatch();
        }
    }

    /**
     * Export a batch of spans
     */
    private void exportBatch() {
        if (isShutdown.get() || spanQueue.isEmpty()) {
            return;
        }

        List<SimpleSpan> batch = new ArrayList<>();
        spanQueue.drainTo(batch, maxExportBatchSize);

        if (!batch.isEmpty()) {
            System.out.printf("[BATCH-PROCESSOR] Exporting batch of %d spans%n", batch.size());
            exportExecutor.submit(() -> {
                try {
                    exporter.exportBatch(batch);
                } catch (Exception e) {
                    System.err.printf("[BATCH-PROCESSOR] Failed to export batch of %d spans: %s%n",
                                     batch.size(), e.getMessage());
                }
            });
        }
    }

    @Override
    public boolean forceFlush() {
        if (isShutdown.get()) {
            return false;
        }

        try {
            // Export all pending spans
            List<SimpleSpan> remainingSpans = new ArrayList<>();
            spanQueue.drainTo(remainingSpans);

            if (!remainingSpans.isEmpty()) {
                // Process in batches
                for (int i = 0; i < remainingSpans.size(); i += maxExportBatchSize) {
                    int endIndex = Math.min(i + maxExportBatchSize, remainingSpans.size());
                    List<SimpleSpan> batch = remainingSpans.subList(i, endIndex);

                    Future<?> future = exportExecutor.submit(() -> exporter.exportBatch(batch));
                    future.get(exportTimeoutMillis, TimeUnit.MILLISECONDS);
                }
            }

            return true;
        } catch (Exception e) {
            System.err.printf("[BATCH-PROCESSOR] Failed to force flush: %s%n", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            System.out.println("[BATCH-PROCESSOR] Shutting down...");

            try {
                // Force flush remaining spans
                forceFlush();

                // Shutdown executors
                scheduler.shutdown();
                exportExecutor.shutdown();

                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }

                if (!exportExecutor.awaitTermination(exportTimeoutMillis, TimeUnit.MILLISECONDS)) {
                    exportExecutor.shutdownNow();
                }

                System.out.println("[BATCH-PROCESSOR] Shutdown complete");
                return true;

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
                exportExecutor.shutdownNow();
                System.err.println("[BATCH-PROCESSOR] Shutdown interrupted");
                return false;
            }
        }
        return true;
    }

    /**
     * Get the current queue size
     */
    public int getQueueSize() {
        return spanQueue.size();
    }

    /**
     * Check if the processor is shutdown
     */
    public boolean isShutdown() {
        return isShutdown.get();
    }
}
