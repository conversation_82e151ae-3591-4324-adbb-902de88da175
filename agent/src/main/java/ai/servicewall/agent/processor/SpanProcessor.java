package ai.servicewall.agent.processor;

import ai.servicewall.agent.span.SimpleSpan;

/**
 * SpanProcessor interface following OpenTelemetry patterns
 * Handles span lifecycle events for processing and export
 */
public interface SpanProcessor {

    /**
     * Called when a span is started
     * @param span The span that was started
     */
    void onStart(SimpleSpan span);

    /**
     * Called when a span is ended
     * @param span The span that was ended
     */
    void onEnd(SimpleSpan span);

    /**
     * Force flush any pending spans
     * @return true if all spans were successfully flushed
     */
    boolean forceFlush();

    /**
     * Shutdown the processor and release resources
     * @return true if shutdown was successful
     */
    boolean shutdown();
}
