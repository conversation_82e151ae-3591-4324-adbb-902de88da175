package ai.servicewall.agent;

import java.io.File;
import java.lang.instrument.Instrumentation;

import ai.servicewall.agent.config.AgentConfig;
import ai.servicewall.agent.config.DataCaptureConfig;
import ai.servicewall.agent.exporter.OtlpSpanExporter;
import ai.servicewall.agent.instrumentation.EntityPostLoadInstrumentation;
import ai.servicewall.agent.instrumentation.HibernateInstrumentation;
import ai.servicewall.agent.instrumentation.JdbcInstrumentation;
import ai.servicewall.agent.instrumentation.ResultSetCloseInstrumentation;
import ai.servicewall.agent.instrumentation.ResultSetInstrumentation;
import ai.servicewall.agent.instrumentation.ResultSetObjectInstrumentation;
import ai.servicewall.agent.instrumentation.SpringControllerInstrumentation;
import ai.servicewall.agent.instrumentation.UserEntityInstrumentation;
import ai.servicewall.agent.processor.BatchSpanProcessor;
import ai.servicewall.agent.processor.SpanProcessor;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.dynamic.loading.ClassInjector;
import net.bytebuddy.matcher.ElementMatchers;
import net.bytebuddy.utility.JavaModule;

/**
 * Enhanced OpenTelemetry Java Agent with Jaeger integration
 * 基于 ByteBuddy 实现零侵入式追踪，支持 OTLP 导出到 Jaeger
 */
public class Agent {

        private static volatile SpanProcessor spanProcessor;
        private static volatile OtlpSpanExporter otlpExporter;
        private static final Object initLock = new Object();

        public static void premain(String agentArgs, Instrumentation inst) {
                try {
                        // Initialize configuration
                        AgentConfig config = AgentConfig.getInstance();
                        config.validate();
                        config.printConfig();

                        // Initialize data capture configuration
                        DataCaptureConfig dataCaptureConfig = DataCaptureConfig.getInstance();
                        dataCaptureConfig.validate();
                        dataCaptureConfig.printConfig();

                        // Initialize exporters and processors
                        initializeExporters(config);

                        // Install instrumentations
                        installInstrumentations(inst);

                        // Add shutdown hook
                        addShutdownHook();

                        System.out.println("[Agent] ✅ OpenTelemetry Minimal Agent Started Successfully");

                } catch (Exception e) {
                        System.err.println("[Agent] ❌ Failed to start agent: " + e.getMessage());
                        e.printStackTrace();
                }
        }

        /**
         * Initialize OTLP exporter and span processor
         */
        private static void initializeExporters(AgentConfig config) {
                synchronized (initLock) {
                        if (config.isOtlpEnabled()) {
                                // Initialize OTLP exporter
                                otlpExporter = new OtlpSpanExporter(config.getOtlpEndpoint());

                                // Initialize batch span processor
                                spanProcessor = new BatchSpanProcessor(
                                                otlpExporter,
                                                config.getBatchSize(),
                                                config.getExportTimeoutMs(),
                                                config.getBatchDelayMs(),
                                                config.getMaxQueueSize());

                                System.out.printf("[Agent] Initialized OTLP exporter: %s%n", config.getOtlpEndpoint());
                        } else {
                                System.out.println("[Agent] OTLP exporter disabled");
                        }
                }
        }

        /**
         * Add shutdown hook for proper cleanup
         */
        private static void addShutdownHook() {
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                        System.out.println("[Agent] Shutting down...");

                        if (spanProcessor != null) {
                                spanProcessor.shutdown();
                        }

                        if (otlpExporter != null) {
                                otlpExporter.shutdown();
                        }

                        System.out.println("[Agent] Shutdown complete");
                }, "agent-shutdown-hook"));
        }

        /**
         * Get the current span processor (for use by spans)
         */
        public static SpanProcessor getSpanProcessor() {
                return spanProcessor;
        }

        /**
         * 将Advice类注入到bootstrap classloader中，确保早期加载的JDBC类可以访问
         */
        private static void injectAdviceClassesToBootstrap(Instrumentation inst) {
                try {
                        // 获取临时目录
                        File temp = new File(System.getProperty("java.io.tmpdir"));

                        // 需要注入的advice类列表
                        Class<?>[] adviceClasses = {
                                        ResultSetInstrumentation.class,
                                        ResultSetObjectInstrumentation.class,
                                        JdbcInstrumentation.class,
                                        SpringControllerInstrumentation.class,
                                        HibernateInstrumentation.class,
                                        EntityPostLoadInstrumentation.class,
                                        UserEntityInstrumentation.class
                        };

                        // 将所有advice类注入bootstrap classloader
                        java.util.Map<TypeDescription, byte[]> classMap = new java.util.HashMap<>();
                        for (Class<?> adviceClass : adviceClasses) {
                                // 读取类文件字节码
                                java.io.InputStream is = adviceClass.getClassLoader()
                                                .getResourceAsStream(
                                                                adviceClass.getName().replace('.', '/') + ".class");
                                if (is != null) {
                                        byte[] classBytes = new byte[is.available()];
                                        is.read(classBytes);
                                        is.close();

                                        classMap.put(new TypeDescription.ForLoadedType(adviceClass), classBytes);
                                        System.err.println(
                                                        "[Agent] 📦 Prepared advice class for bootstrap injection: "
                                                                        + adviceClass.getName());
                                }
                        }

                        // 执行注入
                        ClassInjector.UsingInstrumentation
                                        .of(temp, ClassInjector.UsingInstrumentation.Target.BOOTSTRAP, inst)
                                        .inject(classMap);

                        System.err.println(
                                        "[Agent] ✅ Successfully injected " + classMap.size()
                                                        + " advice classes to bootstrap classloader");
                        System.err.flush();

                } catch (Exception e) {
                        System.err
                                        .println("[Agent] ⚠️ Failed to inject advice classes to bootstrap classloader: "
                                                        + e.getMessage());
                        System.err.println("[Agent] Continuing with regular classloader approach...");
                        e.printStackTrace();
                }
        }

        /**
         * 安装所有插桩
         */
        private static void installInstrumentations(Instrumentation inst) {
                // 首先将advice类注入bootstrap classloader
                injectAdviceClassesToBootstrap(inst);

                AgentBuilder agentBuilder = new AgentBuilder.Default()
                                // 启用类重定义策略以支持已加载的类
                                .with(AgentBuilder.RedefinitionStrategy.RETRANSFORMATION)
                                // 禁用类型检查以避免问题
                                .with(AgentBuilder.DescriptionStrategy.Default.POOL_ONLY)
                                // 忽略 JDK 内部类和 agent 自身的类
                                .ignore(ElementMatchers.nameStartsWith("java."))
                                .ignore(ElementMatchers.nameStartsWith("javax."))
                                .ignore(ElementMatchers.nameStartsWith("sun."))
                                .ignore(ElementMatchers.nameStartsWith("com.sun."))
                                .ignore(ElementMatchers.nameStartsWith("ai.servicewall.agent."))
                                .ignore(ElementMatchers.nameStartsWith("net.bytebuddy."))
                                .ignore(ElementMatchers.nameStartsWith("jdk."))
                                // 添加详细的 listener 来调试拦截过程
                                .with(new AgentBuilder.Listener() {
                                        @Override
                                        public void onDiscovery(String typeName, ClassLoader classLoader,
                                                        JavaModule module,
                                                        boolean loaded) {
                                                if (typeName.contains("ResultSet") || typeName.contains("postgresql")
                                                                || typeName.contains("hikari")) {
                                                        System.err.println(
                                                                        "[Agent] 🔍 Discovered "
                                                                                        + (loaded ? "loaded" : "new")
                                                                                        + " class: " + typeName);
                                                        System.err.flush();
                                                }
                                        }

                                        @Override
                                        public void onTransformation(TypeDescription typeDescription,
                                                        ClassLoader classLoader,
                                                        JavaModule module, boolean loaded, DynamicType dynamicType) {
                                                if (typeDescription.getName().contains("ResultSet")
                                                                || typeDescription.getName().contains("postgresql")
                                                                || typeDescription.getName().contains("hikari")) {
                                                        System.err.println("[Agent] 🎯 Transformed "
                                                                        + (loaded ? "loaded" : "new") + " class: "
                                                                        + typeDescription.getName());
                                                        System.err.flush();
                                                }
                                        }

                                        @Override
                                        public void onIgnored(TypeDescription typeDescription, ClassLoader classLoader,
                                                        JavaModule module,
                                                        boolean loaded) {
                                                // 不需要记录忽略的类
                                        }

                                        @Override
                                        public void onError(String typeName, ClassLoader classLoader, JavaModule module,
                                                        boolean loaded,
                                                        Throwable throwable) {
                                                System.err.println(
                                                                "[Agent] ❌ Error transforming class: " + typeName
                                                                                + " - " + throwable.getMessage());
                                                System.err.flush();
                                        }

                                        @Override
                                        public void onComplete(String typeName, ClassLoader classLoader,
                                                        JavaModule module,
                                                        boolean loaded) {
                                                // 不需要记录完成的类
                                        }
                                });

                // 1. Spring Controller 插桩 - 只拦截HTTP映射注解的方法
                agentBuilder = agentBuilder
                                .type(ElementMatchers.isAnnotatedWith(
                                                ElementMatchers.named(
                                                                "org.springframework.web.bind.annotation.RestController")
                                                                .or(ElementMatchers.named(
                                                                                "org.springframework.stereotype.Controller"))))
                                .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                                        System.out.println("[Agent] Instrumenting Spring Controller: "
                                                        + typeDescription.getName());
                                        return builder
                                                        .method(ElementMatchers.isPublic()
                                                                        .and(ElementMatchers.not(ElementMatchers
                                                                                        .isConstructor()))
                                                                        .and(ElementMatchers.isAnnotatedWith(
                                                                                        ElementMatchers
                                                                                                        .named("org.springframework.web.bind.annotation.RequestMapping")
                                                                                                        .or(ElementMatchers
                                                                                                                        .named(
                                                                                                                                        "org.springframework.web.bind.annotation.GetMapping"))
                                                                                                        .or(ElementMatchers
                                                                                                                        .named(
                                                                                                                                        "org.springframework.web.bind.annotation.PostMapping"))
                                                                                                        .or(ElementMatchers
                                                                                                                        .named(
                                                                                                                                        "org.springframework.web.bind.annotation.PutMapping"))
                                                                                                        .or(ElementMatchers
                                                                                                                        .named(
                                                                                                                                        "org.springframework.web.bind.annotation.DeleteMapping"))
                                                                                                        .or(ElementMatchers
                                                                                                                        .named(
                                                                                                                                        "org.springframework.web.bind.annotation.PatchMapping")))))
                                                        .intercept(Advice.to(SpringControllerInstrumentation.class));
                                });

                // 2. JDBC PreparedStatement 插桩 - 使用更宽泛的匹配条件
                agentBuilder = agentBuilder
                                .type(ElementMatchers.hasSuperType(
                                                ElementMatchers.named("java.sql.PreparedStatement"))
                                                .and(ElementMatchers.not(ElementMatchers.isInterface()))
                                                .and(ElementMatchers.not(ElementMatchers.isAbstract())))
                                .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                                        System.out
                                                        .println("[Agent] *** Instrumenting JDBC PreparedStatement: "
                                                                        + typeDescription.getName());
                                        return builder
                                                        .method(ElementMatchers.nameStartsWith("execute")
                                                                        .and(ElementMatchers.isPublic())
                                                                        .and(ElementMatchers.not(
                                                                                        ElementMatchers.isAbstract()))
                                                                        .and(ElementMatchers.not(ElementMatchers
                                                                                        .named("executeWithFlags")))) // 排除
                                                                                                                      // executeWithFlags
                                                                                                                      // 方法
                                                        .intercept(Advice.to(JdbcInstrumentation.class));
                                });

                // 3. ResultSet 数据脱敏插桩（适配所有实现，包括 HikariCP 代理） - getString 和 getObject 方法
                agentBuilder = agentBuilder
                                .type(ElementMatchers.hasSuperType(ElementMatchers.named("java.sql.ResultSet"))
                                                .and(ElementMatchers.not(ElementMatchers.isInterface()))
                                                .and(ElementMatchers.not(ElementMatchers.isAbstract())))
                                .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                                        System.err.println(
                                                        "[Agent] 🛡️ Instrumenting ResultSet for data masking: "
                                                                        + typeDescription.getName());
                                        System.err.flush();
                                        return builder
                                                        // 拦截 getString 方法
                                                        .method(ElementMatchers.named("getString")
                                                                        .and(ElementMatchers
                                                                                        .takesArguments(int.class)
                                                                                        .or(ElementMatchers
                                                                                                        .takesArguments(String.class)))
                                                                        .and(ElementMatchers.returns(String.class)))
                                                        .intercept(Advice.to(ResultSetInstrumentation.class))
                                                        // 拦截 getObject 方法 - 使用专门的 ObjectInstrumentation
                                                        .method(ElementMatchers.named("getObject")
                                                                        .and(ElementMatchers
                                                                                        .takesArguments(int.class)
                                                                                        .or(ElementMatchers
                                                                                                        .takesArguments(String.class)))
                                                                        .and(ElementMatchers.returns(Object.class)))
                                                        .intercept(Advice.to(ResultSetObjectInstrumentation.class))
                                                        // 拦截所有 get* 方法来调试 Hibernate 实际使用的方法
                                                        .method(ElementMatchers.nameStartsWith("get")
                                                                        .and(ElementMatchers.not(ElementMatchers.named("getString")))
                                                                        .and(ElementMatchers.not(ElementMatchers.named("getObject")))
                                                                        .and(ElementMatchers.not(ElementMatchers.nameStartsWith("getMetaData")))
                                                                        .and(ElementMatchers.not(ElementMatchers.nameStartsWith("getType")))
                                                                        .and(ElementMatchers.takesArguments(int.class)
                                                                                        .or(ElementMatchers.takesArguments(String.class))))
                                                        .intercept(Advice.to(ResultSetObjectInstrumentation.class)); // 使用 Object 拦截器来调试
                                });

                // 3b. ResultSet close() 方法拦截 - 用于清理 JdbcSpanStorage（适配所有实现）
                agentBuilder = agentBuilder
                                .type(ElementMatchers.hasSuperType(ElementMatchers.named("java.sql.ResultSet"))
                                                .and(ElementMatchers.not(ElementMatchers.isInterface()))
                                                .and(ElementMatchers.not(ElementMatchers.isAbstract())))
                                .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                                        return builder
                                                        // 拦截 close 方法进行清理
                                                        .method(ElementMatchers.named("close")
                                                                        .and(ElementMatchers.takesArguments(0)))
                                                        .intercept(Advice.to(ResultSetCloseInstrumentation.class));
                                });

                // 4. HikariCP Proxy ResultSet 数据脱敏插桩
                // agentBuilder = agentBuilder
                // .type(ElementMatchers.hasSuperType(ElementMatchers.named("java.sql.ResultSet"))
                // .and(ElementMatchers.nameContains("hikari")))
                // .transform((builder, typeDescription, classLoader, module, protectionDomain)
                // -> {
                // System.err.println("[Agent] 🛡️ Instrumenting HikariCP ResultSet for data
                // masking: " + typeDescription.getName());
                // System.err.flush();
                // return builder
                // // 专门拦截getString方法，确保返回值修改生效
                // .visit(Advice.to(ResultSetInstrumentation.class)
                // .on(ElementMatchers.named("getString")
                // .and(ElementMatchers.takesArguments(int.class)
                // .or(ElementMatchers.takesArguments(String.class)))
                // .and(ElementMatchers.returns(String.class))));
                // });

                // 5. Hibernate TwoPhaseLoad 插桩 - 拦截所有方法来发现正确的入口点
                // agentBuilder = agentBuilder
                //                 .type(ElementMatchers.named("org.hibernate.engine.internal.TwoPhaseLoad"))
                //                 .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                //                         System.err.println("[Agent] 🎯 Instrumenting Hibernate TwoPhaseLoad: "
                //                                         + typeDescription.getName());
                //                         System.err.flush();
                //                         return builder
                //                                         .method(ElementMatchers.isStatic()
                //                                                         .and(ElementMatchers.isPublic())
                //                                                         .and(ElementMatchers.not(ElementMatchers
                //                                                                         .isConstructor())))
                //                                         .intercept(Advice.to(HibernateInstrumentation.class));
                //                 });

                // 6. User Entity 数据脱敏插桩 - 在实体对象级别进行脱敏
                agentBuilder = agentBuilder
                                .type(ElementMatchers.named("ai.servicewall.sample.model.User"))
                                .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                                        System.err.println(
                                                        "[Agent] 🎯 Instrumenting User entity for setter-level sanitization: "
                                                                        + typeDescription.getName());
                                        System.err.flush();
                                        return builder
                                                        .method(ElementMatchers.nameStartsWith("set")
                                                                        .and(ElementMatchers.isPublic())
                                                                        .and(ElementMatchers.takesArguments(1))
                                                                        .and(ElementMatchers.not(ElementMatchers.named("setId"))))
                                                        .intercept(Advice.to(UserEntityInstrumentation.class));
                                });
                // agentBuilder = agentBuilder
                //                 .type(ElementMatchers.nameEndsWith("Repository")
                //                                 .or(ElementMatchers.nameEndsWith("RepositoryImpl"))
                //                                 .and(ElementMatchers.not(ElementMatchers.isInterface())))
                //                 .transform((builder, typeDescription, classLoader, module, protectionDomain) -> {
                //                         System.err.println(
                //                                         "[Agent] 🎯 Instrumenting JPA Repository for entity post-load sanitization: "
                //                                                         + typeDescription.getName());
                //                         System.err.flush();
                //                         return builder
                //                                         .method(ElementMatchers.isPublic()
                //                                                         .and(ElementMatchers.not(ElementMatchers
                //                                                                         .isConstructor()))
                //                                                         .and(ElementMatchers.nameStartsWith("find")
                //                                                                         .or(ElementMatchers
                //                                                                                         .nameStartsWith("get"))
                //                                                                         .or(ElementMatchers
                //                                                                                         .nameStartsWith("query"))))
                //                                         .intercept(Advice.to(EntityPostLoadInstrumentation.class));
                //                 });

                // .transform((builder, typeDescription, classLoader, module, protectionDomain)
                // -> {
                // System.out.println("[Agent] *** Instrumenting Hibernate TwoPhaseLoad: " +
                // typeDescription.getName());
                // return builder
                // .method(ElementMatchers.named("setPropertyValue")
                // .and(ElementMatchers.isStatic())
                // .and(ElementMatchers.isPublic()))
                // .intercept(Advice.to(HibernateInstrumentation.class));

                // 5. Spring Controller 响应脱敏插桩 (临时禁用，编译问题)
                // agentBuilder = agentBuilder
                // .type(ElementMatchers.nameStartsWith("ai.servicewall.sample.controller.")
                // .and(ElementMatchers.not(ElementMatchers.isInterface())))
                // .transform((builder, typeDescription, classLoader, module, protectionDomain)
                // -> {
                // System.out.println("[Agent] *** Instrumenting Controller for response
                // sanitization: " + typeDescription.getName());
                // return builder
                // .method(ElementMatchers.isPublic()
                // .and(ElementMatchers.returns(ElementMatchers.named("org.springframework.http.ResponseEntity"))))
                // .intercept(Advice.to(HttpResponseInstrumentation.class));
                // });

                // 安装所有插桩
                agentBuilder.installOn(inst);

                System.out.println("[Agent] Installed instrumentations:");
                System.out.println("[Agent] - Spring Controller HTTP tracing (simplified safe version)");
                System.out.println(
                                "[Agent] - JDBC PreparedStatement database tracing with intelligent ResultSet handling");
                System.out.println(
                                "[Agent] - PostgreSQL ResultSet data masking hooks (getString & getObject) for ShardingSphere-style security");
                // System.out.println("[Agent] - Hibernate TwoPhaseLoad entity data capture");
                // System.out.println("[Agent] - HTTP Response data sanitization");
        }
}
