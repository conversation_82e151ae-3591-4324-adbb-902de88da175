package ai.servicewall.agent.exporter;

import ai.servicewall.agent.span.SimpleSpan;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Map;

/**
 * 控制台 Span 导出器
 * 输出符合 OpenTelemetry OTLP 格式的 JSON 数据到控制台
 */
public class ConsoleSpanExporter {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String SERVICE_NAME = "sample-app";
    private static final String AGENT_NAME = "minimal-agent";
    private static final String AGENT_VERSION = "1.0.0";

    /**
     * 导出单个 Span 到控制台
     */
    public static void export(SimpleSpan span) {
        try {
            ObjectNode spanJson = createSpanJson(span);
            ObjectNode otlpJson = wrapInOtlpFormat(spanJson);
            
            String jsonString = OBJECT_MAPPER.writerWithDefaultPrettyPrinter()
                .writeValueAsString(otlpJson);
            
            System.out.println("[OTEL-TRACE] " + jsonString);
            
        } catch (Exception e) {
            System.err.println("[OTEL-ERROR] Failed to export span: " + e.getMessage());
        }
    }

    /**
     * 创建单个 Span 的 JSON 表示
     */
    private static ObjectNode createSpanJson(SimpleSpan span) {
        ObjectNode spanNode = OBJECT_MAPPER.createObjectNode();
        
        spanNode.put("traceId", span.getTraceId());
        spanNode.put("spanId", span.getSpanId());
        
        if (span.getParentSpanId() != null) {
            spanNode.put("parentSpanId", span.getParentSpanId());
        }
        
        spanNode.put("name", span.getOperationName());
        spanNode.put("kind", mapSpanKind(span.getKind()));
        spanNode.put("startTimeUnixNano", span.getStartTimeUnixNano());
        spanNode.put("endTimeUnixNano", span.getEndTimeUnixNano());
        
        // 属性
        ArrayNode attributesArray = OBJECT_MAPPER.createArrayNode();
        for (Map.Entry<String, Object> entry : span.getAttributes().entrySet()) {
            ObjectNode attrNode = OBJECT_MAPPER.createObjectNode();
            attrNode.put("key", entry.getKey());
            attrNode.put("value", entry.getValue().toString());
            attributesArray.add(attrNode);
        }
        spanNode.set("attributes", attributesArray);
        
        // 状态
        ObjectNode statusNode = OBJECT_MAPPER.createObjectNode();
        statusNode.put("code", mapSpanStatus(span.getStatus()));
        spanNode.set("status", statusNode);
        
        return spanNode;
    }

    /**
     * 将 Span JSON 包装成 OTLP 格式
     */
    private static ObjectNode wrapInOtlpFormat(ObjectNode spanJson) {
        ObjectNode root = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceSpans = OBJECT_MAPPER.createArrayNode();
        
        ObjectNode resourceSpan = OBJECT_MAPPER.createObjectNode();
        
        // Resource 信息
        ObjectNode resource = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceAttributes = OBJECT_MAPPER.createArrayNode();
        ObjectNode serviceNameAttr = OBJECT_MAPPER.createObjectNode();
        serviceNameAttr.put("key", "service.name");
        serviceNameAttr.put("value", SERVICE_NAME);
        resourceAttributes.add(serviceNameAttr);
        resource.set("attributes", resourceAttributes);
        resourceSpan.set("resource", resource);
        
        // Scope 信息
        ArrayNode scopeSpans = OBJECT_MAPPER.createArrayNode();
        ObjectNode scopeSpan = OBJECT_MAPPER.createObjectNode();
        
        ObjectNode scope = OBJECT_MAPPER.createObjectNode();
        scope.put("name", AGENT_NAME);
        scope.put("version", AGENT_VERSION);
        scopeSpan.set("scope", scope);
        
        // Spans 数组
        ArrayNode spans = OBJECT_MAPPER.createArrayNode();
        spans.add(spanJson);
        scopeSpan.set("spans", spans);
        
        scopeSpans.add(scopeSpan);
        resourceSpan.set("scopeSpans", scopeSpans);
        
        resourceSpans.add(resourceSpan);
        root.set("resourceSpans", resourceSpans);
        
        return root;
    }

    /**
     * 映射 Span Kind 到 OTLP 字符串
     */
    private static String mapSpanKind(SimpleSpan.SpanKind kind) {
        switch (kind) {
            case INTERNAL: return "SPAN_KIND_INTERNAL";
            case SERVER: return "SPAN_KIND_SERVER";
            case CLIENT: return "SPAN_KIND_CLIENT";
            case PRODUCER: return "SPAN_KIND_PRODUCER";
            case CONSUMER: return "SPAN_KIND_CONSUMER";
            default: return "SPAN_KIND_UNSPECIFIED";
        }
    }

    /**
     * 映射 Span Status 到 OTLP 字符串
     */
    private static String mapSpanStatus(SimpleSpan.SpanStatus status) {
        switch (status) {
            case OK: return "STATUS_CODE_OK";
            case ERROR: return "STATUS_CODE_ERROR";
            case UNSET: 
            default: return "STATUS_CODE_UNSET";
        }
    }

    /**
     * 简化版本 - 仅输出关键信息到控制台
     */
    public static void exportSimple(SimpleSpan span) {
        String parentInfo = span.getParentSpanId() != null 
            ? " parentSpanId=" + span.getParentSpanId() 
            : " (root)";
            
        System.out.printf("[TRACE] traceId=%s spanId=%s%s name='%s' kind=%s duration=%dms%n",
            span.getTraceId(),
            span.getSpanId(),
            parentInfo,
            span.getOperationName(),
            span.getKind(),
            span.getDurationMs());
            
        // 输出属性
        if (!span.getAttributes().isEmpty()) {
            System.out.print("  attributes: ");
            span.getAttributes().forEach((key, value) -> 
                System.out.printf("%s=%s ", key, value));
            System.out.println();
        }
    }
}