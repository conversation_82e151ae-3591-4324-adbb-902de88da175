package ai.servicewall.agent.exporter;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import ai.servicewall.agent.span.SimpleSpan;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * OTLP HTTP Exporter for sending traces to Jaeger
 * Implements OpenTelemetry Protocol over HTTP
 */
public class OtlpSpanExporter {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String SERVICE_NAME = "sample-app";
    private static final String AGENT_NAME = "minimal-agent";
    private static final String AGENT_VERSION = "1.0.0";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient httpClient;
    private final String endpoint;
    private final boolean enabled;

    public OtlpSpanExporter(String endpoint) {
        this.endpoint = endpoint != null ? endpoint : "http://localhost:4318/v1/traces";
        this.enabled = endpoint != null && !endpoint.trim().isEmpty();
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();
    }

    /**
     * Export a single span to Jaeger via OTLP HTTP
     */
    public void export(SimpleSpan span) {
        if (!enabled) {
            System.out.println("[OTLP-SKIP] OTLP exporter disabled - no endpoint configured");
            return;
        }

        try {
            ObjectNode otlpPayload = createOtlpPayload(span);
            String json = OBJECT_MAPPER.writeValueAsString(otlpPayload);

            RequestBody body = RequestBody.create(json, JSON);
            Request request = new Request.Builder()
                .url(endpoint)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    System.out.printf("[OTLP-SUCCESS] Exported span to Jaeger: traceId=%s spanId=%s%n",
                        span.getTraceId(), span.getSpanId());
                } else {
                    System.err.printf("[OTLP-ERROR] Failed to export span: HTTP %d - %s%n",
                        response.code(), response.message());
                }
            }
        } catch (IOException e) {
            System.err.printf("[OTLP-ERROR] Network error exporting span: %s%n", e.getMessage());
        } catch (Exception e) {
            System.err.printf("[OTLP-ERROR] Unexpected error exporting span: %s%n", e.getMessage());
        }
    }

    /**
     * Export multiple spans in a batch
     */
    public void exportBatch(List<SimpleSpan> spans) {
        if (!enabled || spans.isEmpty()) {
            return;
        }

        try {
            ObjectNode otlpPayload = createOtlpBatchPayload(spans);
            String json = OBJECT_MAPPER.writeValueAsString(otlpPayload);

            System.out.println("output json: " + json);

            RequestBody body = RequestBody.create(json, JSON);
            Request request = new Request.Builder()
                .url(endpoint)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    System.out.printf("[OTLP-BATCH-SUCCESS] Exported %d spans to Jaeger%n", spans.size());
                } else {
                    System.err.printf("[OTLP-BATCH-ERROR] Failed to export batch: HTTP %d - %s%n",
                        response.code(), response.message());
                }
            }
        } catch (Exception e) {
            System.err.printf("[OTLP-BATCH-ERROR] Error exporting span batch: %s%n", e.getMessage());
        }
    }

    /**
     * Create OTLP-compliant JSON payload for a single span
     */
    private ObjectNode createOtlpPayload(SimpleSpan span) {
        ObjectNode root = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceSpans = OBJECT_MAPPER.createArrayNode();

        ObjectNode resourceSpan = OBJECT_MAPPER.createObjectNode();

        // Resource information
        ObjectNode resource = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceAttributes = OBJECT_MAPPER.createArrayNode();

        ObjectNode serviceNameAttr = OBJECT_MAPPER.createObjectNode();
        serviceNameAttr.put("key", "service.name");
        ObjectNode serviceNameValue = OBJECT_MAPPER.createObjectNode();
        serviceNameValue.put("stringValue", SERVICE_NAME);
        serviceNameAttr.set("value", serviceNameValue);
        resourceAttributes.add(serviceNameAttr);

        resource.set("attributes", resourceAttributes);
        resourceSpan.set("resource", resource);

        // Scope spans
        ArrayNode scopeSpans = OBJECT_MAPPER.createArrayNode();
        ObjectNode scopeSpan = OBJECT_MAPPER.createObjectNode();

        ObjectNode scope = OBJECT_MAPPER.createObjectNode();
        scope.put("name", AGENT_NAME);
        scope.put("version", AGENT_VERSION);
        scopeSpan.set("scope", scope);

        // Spans array
        ArrayNode spans = OBJECT_MAPPER.createArrayNode();
        spans.add(createSpanJson(span));
        scopeSpan.set("spans", spans);

        scopeSpans.add(scopeSpan);
        resourceSpan.set("scopeSpans", scopeSpans);

        resourceSpans.add(resourceSpan);
        root.set("resourceSpans", resourceSpans);

        return root;
    }

    /**
     * Create OTLP-compliant JSON payload for multiple spans
     */
    private ObjectNode createOtlpBatchPayload(List<SimpleSpan> spanList) {
        ObjectNode root = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceSpans = OBJECT_MAPPER.createArrayNode();

        ObjectNode resourceSpan = OBJECT_MAPPER.createObjectNode();

        // Resource information
        ObjectNode resource = OBJECT_MAPPER.createObjectNode();
        ArrayNode resourceAttributes = OBJECT_MAPPER.createArrayNode();

        ObjectNode serviceNameAttr = OBJECT_MAPPER.createObjectNode();
        serviceNameAttr.put("key", "service.name");
        ObjectNode serviceNameValue = OBJECT_MAPPER.createObjectNode();
        serviceNameValue.put("stringValue", SERVICE_NAME);
        serviceNameAttr.set("value", serviceNameValue);
        resourceAttributes.add(serviceNameAttr);

        resource.set("attributes", resourceAttributes);
        resourceSpan.set("resource", resource);

        // Scope spans
        ArrayNode scopeSpans = OBJECT_MAPPER.createArrayNode();
        ObjectNode scopeSpan = OBJECT_MAPPER.createObjectNode();

        ObjectNode scope = OBJECT_MAPPER.createObjectNode();
        scope.put("name", AGENT_NAME);
        scope.put("version", AGENT_VERSION);
        scopeSpan.set("scope", scope);

        // Convert all spans to JSON
        ArrayNode spans = OBJECT_MAPPER.createArrayNode();
        for (SimpleSpan span : spanList) {
            spans.add(createSpanJson(span));
        }
        scopeSpan.set("spans", spans);

        scopeSpans.add(scopeSpan);
        resourceSpan.set("scopeSpans", scopeSpans);

        resourceSpans.add(resourceSpan);
        root.set("resourceSpans", resourceSpans);

        return root;
    }

    /**
     * Create JSON representation of a single span following OTLP spec
     */
    private ObjectNode createSpanJson(SimpleSpan span) {
        ObjectNode spanNode = OBJECT_MAPPER.createObjectNode();

        spanNode.put("traceId", span.getTraceId());
        spanNode.put("spanId", span.getSpanId());

        if (span.getParentSpanId() != null) {
            spanNode.put("parentSpanId", span.getParentSpanId());
        }

        spanNode.put("name", span.getOperationName());
        spanNode.put("kind", mapSpanKind(span.getKind()));
        spanNode.put("startTimeUnixNano", span.getStartTimeUnixNano());
        spanNode.put("endTimeUnixNano", span.getEndTimeUnixNano());

        // Attributes
        ArrayNode attributesArray = OBJECT_MAPPER.createArrayNode();
        for (Map.Entry<String, Object> entry : span.getAttributes().entrySet()) {
            ObjectNode attrNode = OBJECT_MAPPER.createObjectNode();
            attrNode.put("key", entry.getKey());

            ObjectNode valueNode = OBJECT_MAPPER.createObjectNode();
            valueNode.put("stringValue", entry.getValue().toString());
            attrNode.set("value", valueNode);

            attributesArray.add(attrNode);
        }
        spanNode.set("attributes", attributesArray);

        // Status
        ObjectNode statusNode = OBJECT_MAPPER.createObjectNode();
        statusNode.put("code", mapSpanStatus(span.getStatus()));
        spanNode.set("status", statusNode);

        return spanNode;
    }

    /**
     * Map SpanKind to OTLP numeric representation
     */
    private int mapSpanKind(SimpleSpan.SpanKind kind) {
        switch (kind) {
            case INTERNAL: return 1;
            case SERVER: return 2;
            case CLIENT: return 3;
            case PRODUCER: return 4;
            case CONSUMER: return 5;
            default: return 0; // SPAN_KIND_UNSPECIFIED
        }
    }

    /**
     * Map SpanStatus to OTLP numeric representation
     */
    private int mapSpanStatus(SimpleSpan.SpanStatus status) {
        switch (status) {
            case UNSET: return 0; // STATUS_CODE_UNSET
            case OK: return 1;    // STATUS_CODE_OK
            case ERROR: return 2; // STATUS_CODE_ERROR
            default: return 0;
        }
    }

    /**
     * Shutdown the exporter and release resources
     */
    public void shutdown() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

    public String getEndpoint() {
        return endpoint;
    }
}
