plugins {
    id("java")
}

group = "ai.servicewall.agent"
version = "1.0.0"

repositories {
    mavenCentral()
}

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

dependencies {
    // Bytecode instrumentation
    implementation("net.bytebuddy:byte-buddy:1.15.4") // 支持 Java 21

    // JSON processing
    implementation("com.fasterxml.jackson.core:jackson-core:2.11.4")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.11.4")

    // OpenTelemetry SDK and OTLP exporter for Jaeger integration
    implementation("io.opentelemetry:opentelemetry-api:1.40.0")
    implementation("io.opentelemetry:opentelemetry-sdk:1.40.0")
    implementation("io.opentelemetry:opentelemetry-exporter-otlp:1.40.0")
    implementation("io.opentelemetry:opentelemetry-semconv:1.25.0-alpha")

    // HTTP client for OTLP exporter
    implementation("com.squareup.okhttp3:okhttp:4.12.0")

    // Compile-only dependencies for instrumentation
    compileOnly("org.springframework:spring-web:5.3.21")
    compileOnly("javax.servlet:javax.servlet-api:4.0.1")

    // Test dependencies
    testImplementation(platform("org.junit:junit-bom:5.10.0"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

tasks.test {
    useJUnitPlatform()
}

tasks.jar {
    manifest {
        attributes(
            "Premain-Class" to "ai.servicewall.agent.Agent",
            "Can-Redefine-Classes" to "true",
            "Can-Retransform-Classes" to "true"
        )
    }

    // 创建 Fat JAR 包含所有依赖
    from(configurations.runtimeClasspath.get().map { if (it.isDirectory) it else zipTree(it) }) {
        exclude("META-INF/*.RSA", "META-INF/*.SF", "META-INF/*.DSA")
        exclude("META-INF/DEPENDENCIES")
        exclude("META-INF/LICENSE*")
        exclude("META-INF/NOTICE*")
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
