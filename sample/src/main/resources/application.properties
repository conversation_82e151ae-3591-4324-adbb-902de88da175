# Database Configuration
spring.datasource.url=***************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Debug logging
# logging.level.org.hibernate=DEBUG
# logging.level.org.springframework.data.jpa=DEBUG
# logging.level.ai.servicewall.sample=DEBUG

# Server Configuration
server.port=8080
