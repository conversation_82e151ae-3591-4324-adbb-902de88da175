package ai.servicewall.sample.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import ai.servicewall.sample.dto.LoginRequest;
import ai.servicewall.sample.dto.LoginResponse;
import ai.servicewall.sample.dto.RegisterRequest;
import ai.servicewall.sample.model.User;
import ai.servicewall.sample.repository.UserRepository;

@RestController
@RequestMapping("/api/auth")
public class UserController {

    @Autowired
    private UserRepository userRepository;

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();

        // 验证输入参数
        if (username == null || username.trim().isEmpty() ||
            password == null || password.trim().isEmpty()) {
            LoginResponse response = new LoginResponse(false, "用户名和密码不能为空", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        // 查找用户
        Optional<User> userOptional = userRepository.findByUsername(username);
        if (userOptional.isEmpty()) {
            LoginResponse response = new LoginResponse(false, "用户不存在", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        User user = userOptional.get();
        // 验证密码
        if (!user.getPassword().equals(password)) {
            LoginResponse response = new LoginResponse(false, "密码错误", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        // 登录成功，生成模拟token
        String token = "Bearer_" + UUID.randomUUID().toString().replace("-", "");

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getRole()
        );

        LoginResponse response = new LoginResponse(true, "登录成功", token, userInfo);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/register")
    public ResponseEntity<LoginResponse> register(@RequestBody RegisterRequest registerRequest) {
        String username = registerRequest.getUsername();
        String email = registerRequest.getEmail();
        String password = registerRequest.getPassword();
        String role = registerRequest.getRole();

        // 验证输入参数
        if (username == null || username.trim().isEmpty() ||
            email == null || email.trim().isEmpty() ||
            password == null || password.trim().isEmpty()) {
            LoginResponse response = new LoginResponse(false, "用户名、邮箱和密码不能为空", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            LoginResponse response = new LoginResponse(false, "用户名已存在", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(email)) {
            LoginResponse response = new LoginResponse(false, "邮箱已存在", null, null);
            return ResponseEntity.badRequest().body(response);
        }

        // 设置默认角色
        if (role == null || role.trim().isEmpty()) {
            role = "USER";
        }

        // 创建新用户
        User newUser = new User(username, email, password, role);
        User savedUser = userRepository.save(newUser);

        // 生成token
        String token = "Bearer_" + UUID.randomUUID().toString().replace("-", "");

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            savedUser.getId(),
            savedUser.getUsername(),
            savedUser.getEmail(),
            savedUser.getRole()
        );

        LoginResponse response = new LoginResponse(true, "注册成功", token, userInfo);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> getUsers() {
        System.out.println("[DEBUG] Starting getUsers() method");
        List<User> users = userRepository.findAll();
        // System.out.println("[DEBUG] Repository returned: " + users.size() + " users");
        // if (users.isEmpty()) {
        //     System.out.println("[DEBUG] Users list is empty!");
        // } else {
        //     System.out.println("[DEBUG] First user: " + users.get(0).getUsername());
        //     for (User user : users) {
        //         System.out.println("[DEBUG] User: id=" + user.getId() + ", username=" + user.getUsername() + ", email=" + user.getEmail());
        //     }
        // }
        Map<String, Object> result = new HashMap<>();
        result.put("users", users);
        result.put("message", "获取用户列表成功");
        System.out.println("[DEBUG] Response map contains " + ((List<?>)result.get("users")).size() + " users");
        return ResponseEntity.ok(result);
    }
}
