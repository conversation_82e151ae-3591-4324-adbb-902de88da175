#!/bin/bash

# OpenTelemetry Minimal Java Agent 启动脚本

set -e

docker-compose down -v
sleep 3
docker-compose up -d
sleep 3
# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="${SCRIPT_DIR}"

echo "========================================"
echo "🚀 OpenTelemetry Minimal Agent Demo"
echo "========================================"
echo "Project Directory: ${PROJECT_DIR}"

# 检查 Java 版本
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安装，请先安装 Java 8+"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | awk -F '"' '{print $2}')
echo "Java Version: ${JAVA_VERSION}"

# 构建 Agent JAR (Fat JAR)
 ./gradlew :agent:jar
AGENT_JAR="${PROJECT_DIR}/agent/build/libs/agent-1.0.0.jar"
if [ ! -f "${AGENT_JAR}" ]; then
    echo "📦 Agent JAR 不存在，正在构建..."
    cd "${PROJECT_DIR}"
     ./gradlew :agent:jar
    if [ $? -ne 0 ]; then
        echo "❌ Agent 构建失败"
        exit 1
    fi
    echo "✅ Agent 构建完成: ${AGENT_JAR}"
fi


# 构建 Sample 应用
SAMPLE_JAR="${PROJECT_DIR}/sample/build/libs/sample-1.0.0.jar"
if [ ! -f "${SAMPLE_JAR}" ]; then
    echo "📦 Sample JAR 不存在，正在构建..."
    cd "${PROJECT_DIR}"
    ./gradlew :sample:bootJar
    if [ $? -ne 0 ]; then
        echo "❌ Sample 构建失败"
        exit 1
    fi
    echo "✅ Sample 构建完成: ${SAMPLE_JAR}"
fi

# 检查文件大小
AGENT_SIZE=$(ls -lh "${AGENT_JAR}" | awk '{print $5}')
SAMPLE_SIZE=$(ls -lh "${SAMPLE_JAR}" | awk '{print $5}')

echo ""
echo "📋 构建信息:"
echo "  Agent JAR: ${AGENT_SIZE} (${AGENT_JAR})"
echo "  Sample JAR: ${SAMPLE_SIZE} (${SAMPLE_JAR})"

# 设置 JVM 参数
JVM_OPTS="-Xms256m -Xmx512m"

# 设置 OpenTelemetry 配置
OTEL_OPTS="-Dotel.service.name=sample-app"
OTEL_OPTS="$OTEL_OPTS -Dotel.exporter.otlp.traces.endpoint=http://localhost:4318/v1/traces"
OTEL_OPTS="$OTEL_OPTS -Dotel.exporter.otlp.enabled=true"
OTEL_OPTS="$OTEL_OPTS -Dotel.exporter.console.enabled=true"

# 启动应用
echo ""
echo "🔥 启动 Spring Boot 应用（带 OpenTelemetry Agent）"
echo "========================================"
echo "Agent: OpenTelemetry Minimal Agent v1.0.0"
echo "Target: Sample Spring Boot Application"
echo "Tracing: HTTP Requests → Database Operations"
echo "Output: Console JSON (OTLP-compatible format)"
echo "========================================"
echo ""

# 运行应用
java ${JVM_OPTS} ${OTEL_OPTS} \
     -javaagent:"${AGENT_JAR}" \
     -jar "${SAMPLE_JAR}" \
     "$@"
