# OpenTelemetry Minimal Java Agent 项目

基于 OpenTelemetry 标准的最小化 Java Agent 实现，专注核心追踪功能。支持 HTTP 请求和数据库操作的零侵入式链路追踪。

## 🎯 项目特色

- ✅ **OpenTelemetry 兼容**：基于 OpenTelemetry 标准实现 Trace、Span、Context
- ✅ **零侵入式追踪**：通过 javaagent 参数自动拦截，无需修改应用代码
- ✅ **轻量化设计**：6MB Fat JAR，专注核心功能
- ✅ **标准输出格式**：OTLP-compatible JSON 格式追踪数据
- ✅ **JDK 1.8+ 支持**：完全兼容 JDK 8 及更高版本
- ✅ **Spring Boot 集成**：自动拦截 Controller 和 JDBC 操作

## 📁 项目结构

```
acadia/
├── agent/                         # OpenTelemetry Minimal Agent
│   ├── src/main/java/ai/servicewall/agent/
│   │   ├── Agent.java                        # 主入口，ByteBuddy 插桩配置
│   │   ├── context/
│   │   │   └── TraceContext.java            # 追踪上下文，ThreadLocal 管理
│   │   ├── span/
│   │   │   └── SimpleSpan.java              # OpenTelemetry Span 实现
│   │   ├── exporter/
│   │   │   └── ConsoleSpanExporter.java     # OTLP 格式 JSON 输出
│   │   └── instrumentation/
│   │       ├── SpringControllerInstrumentation.java  # HTTP 拦截器
│   │       └── JdbcInstrumentation.java              # JDBC 拦截器
│   └── build.gradle.kts                     # Agent 构建配置
├── sample/                        # Spring Boot 示例应用
│   ├── src/main/java/ai/servicewall/sample/
│   │   ├── Main.java                        # 应用入口
│   │   ├── controller/UserController.java
│   │   ├── model/User.java
│   │   ├── repository/UserRepository.java
│   │   └── dto/                            # DTO 模型
│   └── src/main/resources/application.properties
├── docs/specs/java-agent-tracing/  # 设计文档
├── docker-compose.yml              # PostgreSQL 环境
├── run-with-agent.sh               # 自动化启动脚本
└── init.sql                       # 数据库初始化
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 1. 启动 PostgreSQL 数据库
docker-compose up -d

# 2. 运行应用（每次都会重新构建 Agent 和 Sample）
./run-with-agent.sh
```

### 方法二：手动启动

```bash
# 1. 构建 Agent JAR
./gradlew :agent:jar

# 2. 构建 Sample JAR
./gradlew :sample:bootJar

# 3. 启动应用
java -javaagent:agent/build/libs/agent-1.0.0.jar \
     -jar sample/build/libs/sample-1.0.0.jar
```

## 📊 追踪效果展示

启动应用后，Agent 会自动输出 OpenTelemetry 标准格式的追踪数据：

### Agent 启动信息
```
========================================
[Agent] OpenTelemetry Minimal Agent Starting...
[Agent] Version: 1.0.0
[Agent] Based on OpenTelemetry Standards
========================================
[Agent] Installed instrumentations:
[Agent] - Spring Controller HTTP tracing
[Agent] - JDBC PreparedStatement database tracing
[Agent] ✅ OpenTelemetry Minimal Agent Started Successfully
```

### HTTP 请求追踪 (OTLP JSON 格式)
```json
[OTEL-TRACE] {
  "resourceSpans": [{
    "resource": {
      "attributes": [
        {"key": "service.name", "value": "sample-app"}
      ]
    },
    "scopeSpans": [{
      "scope": {
        "name": "minimal-agent",
        "version": "1.0.0"
      },
      "spans": [
        {
          "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
          "spanId": "00f067aa0ba902b7",
          "parentSpanId": "",
          "name": "GET /api/users",
          "kind": "SPAN_KIND_SERVER",
          "startTimeUnixNano": 1692614400000000000,
          "endTimeUnixNano": 1692614400156000000,
          "attributes": [
            {"key": "http.method", "value": "GET"},
            {"key": "http.url", "value": "/api/users"},
            {"key": "http.status_code", "value": "200"},
            {"key": "component", "value": "spring-web"}
          ],
          "status": {"code": "STATUS_CODE_OK"}
        }
      ]
    }]
  }]
}
```

### 数据库操作追踪
```json
[OTEL-TRACE] {
  "spans": [
    {
      "traceId": "4bf92f3577b34da6a3ce929d0e0e4736",
      "spanId": "b9c7c989f97918e1",
      "parentSpanId": "00f067aa0ba902b7",
      "name": "SELECT users",
      "kind": "SPAN_KIND_CLIENT",
      "attributes": [
        {"key": "db.system", "value": "postgresql"},
        {"key": "db.statement", "value": "SELECT * FROM users"},
        {"key": "component", "value": "jdbc"}
      ],
      "status": {"code": "STATUS_CODE_OK"}
    }
  ]
}
```

## 🏗️ OpenTelemetry Agent 架构

### 核心组件映射

| OpenTelemetry 概念 | 最小化实现 | 文件位置 |
|---|---|---|
| `TracerProvider` | 全局单例管理 | `Agent.java` |
| `Tracer` | Span 创建入口 | `SimpleSpan.java` |
| `Span` | 操作执行单元 | `SimpleSpan.java` |
| `SpanContext` | traceId/spanId/traceFlags | `TraceContext.java` |
| `Context` | ThreadLocal 上下文传递 | `TraceContext.java` |
| `SpanExporter` | OTLP 格式输出 | `ConsoleSpanExporter.java` |

### 工作原理

1. **字节码插桩**：Agent 使用 ByteBuddy 在类加载时修改字节码
2. **HTTP 拦截**：拦截 `@RestController` 和 `@Controller` 注解的方法
3. **数据库拦截**：拦截 `PreparedStatement.execute*()` 方法
4. **上下文传播**：ThreadLocal 实现同线程内 Trace 上下文传递
5. **Span 树构建**：HTTP 请求为根 Span，数据库操作为子 Span
6. **标准输出**：按 OpenTelemetry OTLP 格式输出 JSON 追踪数据

## API 测试

Base URL: `http://localhost:8080`

### 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'
```

### 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'
```

### 获取用户列表
```bash
curl http://localhost:8080/api/auth/users
```

每个请求都会在控制台输出完整的 OpenTelemetry 追踪数据，包括 HTTP 请求 Span 和关联的数据库操作 Span。

## 技术规范

### 依赖和版本
- **ByteBuddy**: 1.14.11 (支持 Java 21)
- **Jackson**: 2.11.4 (JSON 序列化)
- **Spring Boot**: 2.7.18 (示例应用)
- **Java**: 1.8+ (向下兼容)

### 构建产物
- `agent/build/libs/agent-1.0.0.jar` (6MB Fat JAR)
- 包含所有依赖，避免类路径冲突
- 支持通过 `-javaagent` 参数直接加载

### OpenTelemetry 语义约定
- **HTTP 属性**: `http.method`, `http.url`, `http.status_code`
- **DB 属性**: `db.system`, `db.statement`, `db.operation`
- **Span Kind**: `SERVER` (HTTP), `CLIENT` (Database)
- **输出格式**: OTLP-compatible JSON

## 开发和扩展

### 添加新的插桩
1. 在 `instrumentation/` 目录创建新的拦截器类
2. 实现 `@Advice.OnMethodEnter` 和 `@Advice.OnMethodExit`
3. 在 `Agent.java` 中注册新的 AgentBuilder 转换器

### 自定义 Span 导出
1. 实现新的 SpanExporter (参考 `ConsoleSpanExporter.java`)
2. 在 `SimpleSpan.end()` 方法中调用新的导出器

## 故障排查

### Agent 加载失败
- 检查 JAR 文件是否存在：`ls -la agent/build/libs/agent-1.0.0.jar`
- 检查 MANIFEST.MF 中的 Premain-Class 配置

### ByteBuddy 兼容性问题
- 确保使用支持当前 Java 版本的 ByteBuddy 版本
- 设置 JVM 参数：`-Dnet.bytebuddy.experimental=true`

### 插桩不生效
- 检查 ByteBuddy 日志中的 DISCOVERY/IGNORE/COMPLETE 信息
- 确认目标类符合 ElementMatchers 匹配条件

---

**基于 OpenTelemetry 标准 | 最小化实现 | 生产就绪的架构模式**
