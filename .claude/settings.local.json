{"permissions": {"allow": ["mcp__context7__get-library-docs", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(java:*)", "<PERSON>sh(./run-with-agent.sh)", "<PERSON><PERSON>(../gradlew:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "mcp__sequential-thinking__sequentialthinking"], "deny": [], "ask": []}}